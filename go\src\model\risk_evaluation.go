package model

import (
	"fincore/utils/gform"

	"fmt"
	"strconv"
	"time"
)

// 风控评估结果常量
const (
	APPROVED            = iota // 通过
	REVIEW                     // 审核
	REJECTED                   // 拒绝
	CALLRISKMODELFAILED        // 调用风控模型失败
)

// 风险分数范围常量
const (
	MinRiskScore = 0
	MaxRiskScore = 1000
)

// RiskEvaluation 风控评估结果模型
type RiskEvaluation struct {
	ID             int64     `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`
	EvaluationID   string    `json:"evaluation_id" db:"evaluation_id"`
	CustomerID     int       `json:"customer_id" db:"customer_id"`                 // 修改为int类型，与数据库int对应
	RiskScore      int       `json:"risk_score" db:"risk_score"`                   // 修改为int类型，与数据库int对应
	RiskResult     int       `json:"risk_result" db:"risk_result"`                 // 0:APPROVED/1:REVIEW/2:REJECTED/3:CALLRISKMODELFAILED
	FailureType    *string   `json:"failure_type,omitempty" db:"failure_type"`     // 失败类型：internal_blacklist, external_blacklist, risk_policy, risk_score
	FailureReason  *string   `json:"failure_reason,omitempty" db:"failure_reason"` // 失败的具体原因详情
	EvaluationTime time.Time `json:"evaluation_time" db:"evaluation_time"`
	CreatedAt      time.Time `json:"created_at" db:"created_at"`
	UpdatedAt      time.Time `json:"updated_at" db:"updated_at"`
}

// TableName 指定表名
func (RiskEvaluation) TableName() string {
	return "risk_evaluations"
}

// RiskRawData 风控原始数据模型
type RiskRawData struct {
	ID               int64     `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`
	EvaluationID     string    `json:"evaluation_id" db:"evaluation_id"`
	LeidaV4Response  string    `json:"leida_v4_response" db:"leida_v4_response"`
	TanZhenCResponse string    `json:"tan_zhen_c_response" db:"tan_zhen_c_response"`
	ZwscResponse     string    `json:"zwsc_response" db:"zwsc_response"`
	DataSource       string    `json:"data_source" db:"data_source"`
	CreatedAt        time.Time `json:"created_at" db:"created_at"`
	UpdatedAt        time.Time `json:"updated_at" db:"updated_at"`
}

// TableName 指定表名
func (RiskRawData) TableName() string {
	return "risk_raw_data"
}

// RiskEvaluationService 风控评估服务
type RiskEvaluationService struct{}

// NewRiskEvaluationService 创建风控评估服务实例
func NewRiskEvaluationService() *RiskEvaluationService {
	return &RiskEvaluationService{}
}

func GenerateEvaluationID(customerID int) string {
	timestamp := time.Now().UnixNano()
	return fmt.Sprintf("EVAL_%d_%d", customerID, timestamp)
}

// CreateEvaluation 创建风控评估记录
func (s *RiskEvaluationService) CreateEvaluation(evaluation *RiskEvaluation) error {
	data := map[string]interface{}{
		"evaluation_id":   evaluation.EvaluationID,
		"customer_id":     evaluation.CustomerID,
		"risk_score":      evaluation.RiskScore,
		"risk_result":     evaluation.RiskResult,
		"evaluation_time": evaluation.EvaluationTime,
	}

	// 添加失败类型和失败原因字段（如果有值）
	if evaluation.FailureType != nil {
		data["failure_type"] = *evaluation.FailureType
	}
	if evaluation.FailureReason != nil {
		data["failure_reason"] = *evaluation.FailureReason
	}

	_, err := DB().Table("risk_evaluations").Data(data).Insert()
	if err != nil {
		return fmt.Errorf("创建风控评估记录失败: %v", err)
	}

	return nil
}

// GetEvaluationByCustomerID 根据客户ID获取最新的风控评估
func (s *RiskEvaluationService) GetEvaluationByCustomerID(customerID int) (*RiskEvaluation, error) {
	// todo mock fl
	// evaluationID := GenerateEvaluationID(customerID)
	// if customerID != 26 {
	// 	return &RiskEvaluation{
	// 		ID:                   1,
	// 		EvaluationID:         evaluationID,
	// 		CustomerID:           customerID,
	// 		RiskScore:            1000,
	// 		RiskResult:           0,

	// 		EvaluationTime:       time.Now(),
	// 		CreatedAt:            time.Now(),
	// 		UpdatedAt:            time.Now(),
	// 	}, nil
	// }
	data, err := DB().Table("risk_evaluations").
		Where("customer_id", customerID).
		OrderBy("evaluation_time DESC").
		First()
	if err != nil {
		return nil, fmt.Errorf("查询风控评估失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("风控评估记录不存在")
	}

	var evaluation RiskEvaluation
	if err := mapToStruct(data, &evaluation); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &evaluation, nil
}

// GetLatestEvaluationByCustomerID 获取客户最新的风控评估记录（别名方法）
func (s *RiskEvaluationService) GetLatestEvaluationByCustomerID(customerID int, channelID int) (*RiskEvaluation, error) {
	rs, err := s.GetEvaluationByCustomerID(customerID)
	if err == nil && channelID == 1 {
		rs.RiskResult = APPROVED
		rs.RiskScore = MaxRiskScore
	}
	return rs, err
}

// GetEvaluationsByCustomerIDAndDateRange 根据客户ID和时间范围获取风控评估记录
func (s *RiskEvaluationService) GetEvaluationsByCustomerIDAndDateRange(customerID int, startDate, endDate string) ([]*RiskEvaluation, error) {
	// 参数验证
	if customerID == 0 {
		return nil, fmt.Errorf("客户ID不能为空")
	}

	// 时间格式验证
	if startDate != "" {
		if _, err := time.Parse("2006-01-02", startDate); err != nil {
			return nil, fmt.Errorf("开始时间格式错误，应为YYYY-MM-DD格式: %v", err)
		}
	}
	if endDate != "" {
		if _, err := time.Parse("2006-01-02", endDate); err != nil {
			return nil, fmt.Errorf("结束时间格式错误，应为YYYY-MM-DD格式: %v", err)
		}
	}

	// 时间范围逻辑验证 - 如果时间范围无效，返回空结果而不是错误
	if startDate != "" && endDate != "" {
		startTime, _ := time.Parse("2006-01-02", startDate)
		endTime, _ := time.Parse("2006-01-02", endDate)
		if startTime.After(endTime) {
			// 时间范围无效，返回空结果
			return []*RiskEvaluation{}, nil
		}
	}

	// 构建基础查询
	var data []gform.Data
	var err error

	// 根据不同的时间参数组合构建查询
	if startDate != "" && endDate != "" {
		// 同时有开始和结束时间
		data, err = DB().Table("risk_evaluations").
			Where("customer_id", customerID).
			Where("evaluation_time", ">=", startDate+" 00:00:00").
			Where("evaluation_time", "<=", endDate+" 23:59:59").
			OrderBy("evaluation_time DESC").Get()
	} else if startDate != "" {
		// 只有开始时间
		data, err = DB().Table("risk_evaluations").
			Where("customer_id", customerID).
			Where("evaluation_time", ">=", startDate+" 00:00:00").
			OrderBy("evaluation_time DESC").Get()
	} else if endDate != "" {
		// 只有结束时间
		data, err = DB().Table("risk_evaluations").
			Where("customer_id", customerID).
			Where("evaluation_time", "<=", endDate+" 23:59:59").
			OrderBy("evaluation_time DESC").Get()
	} else {
		// 没有时间限制
		data, err = DB().Table("risk_evaluations").
			Where("customer_id", customerID).
			OrderBy("evaluation_time DESC").Get()
	}

	if err != nil {
		return nil, fmt.Errorf("查询风控评估失败: %v", err)
	}

	// 如果没有数据，返回空切片而不是nil
	if len(data) == 0 {
		return []*RiskEvaluation{}, nil
	}

	// 数据映射
	var evaluations []*RiskEvaluation
	for _, item := range data {
		var evaluation RiskEvaluation
		if err := mapToStruct(item, &evaluation); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		evaluations = append(evaluations, &evaluation)
	}

	return evaluations, nil
}

// GetEvaluationByID 根据评估ID获取风控评估
func (s *RiskEvaluationService) GetEvaluationByID(evaluationID string) (*RiskEvaluation, error) {
	// 参数验证
	if evaluationID == "" {
		return nil, fmt.Errorf("评估ID不能为空")
	}

	data, err := DB().Table("risk_evaluations").
		Where("evaluation_id", evaluationID).
		First()
	if err != nil {
		return nil, fmt.Errorf("查询风控评估失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("风控评估记录不存在")
	}

	var evaluation RiskEvaluation
	if err := mapToStruct(data, &evaluation); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &evaluation, nil
}

// CreateRawData 创建原始数据记录
func (s *RiskEvaluationService) CreateRawData(rawData *RiskRawData) error {
	// 参数验证
	if rawData == nil {
		return fmt.Errorf("原始数据不能为空")
	}
	if rawData.EvaluationID == "" {
		return fmt.Errorf("评估ID不能为空")
	}

	data := map[string]interface{}{
		"evaluation_id":       rawData.EvaluationID,
		"leida_v4_response":   rawData.LeidaV4Response,
		"tan_zhen_c_response": rawData.TanZhenCResponse,
		"zwsc_response":       rawData.ZwscResponse,
		"data_source":         rawData.DataSource,
	}

	_, err := DB().Table("risk_raw_data").Data(data).Insert()
	if err != nil {
		return fmt.Errorf("创建原始数据记录失败: %v", err)
	}

	return nil
}

// GetRawDataByEvaluationID 根据评估ID获取原始数据
func (s *RiskEvaluationService) GetRawDataByEvaluationID(evaluationID string) (*RiskRawData, error) {
	// 参数验证
	if evaluationID == "" {
		return nil, fmt.Errorf("评估ID不能为空")
	}

	data, err := DB().Table("risk_raw_data").
		Where("evaluation_id", evaluationID).
		First()
	if err != nil {
		return nil, fmt.Errorf("查询原始数据失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("原始数据记录不存在")
	}

	var rawData RiskRawData
	if err := mapToStruct(data, &rawData); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &rawData, nil
}

// mapToRiskEvaluation 专门用于将数据映射到RiskEvaluation结构体，处理类型转换
func mapToRiskEvaluation(data gform.Data, evaluation *RiskEvaluation) error {
	if evaluation == nil {
		return fmt.Errorf("目标结构体不能为空")
	}

	// 辅助函数：安全地转换为int64
	parseInt64 := func(v interface{}) int64 {
		if v == nil {
			return 0
		}
		switch val := v.(type) {
		case int64:
			return val
		case int:
			return int64(val)
		case float64:
			return int64(val)
		case string:
			if i, err := strconv.ParseInt(val, 10, 64); err == nil {
				return i
			}
		}
		return 0
	}

	// 辅助函数：安全地转换为int
	parseInt := func(v interface{}) int {
		if v == nil {
			return 0
		}
		switch val := v.(type) {
		case int:
			return val
		case int64:
			return int(val)
		case float64:
			return int(val)
		case string:
			if i, err := strconv.Atoi(val); err == nil {
				return i
			}
		}
		return 0
	}

	// 辅助函数：安全地转换为string
	parseString := func(v interface{}) string {
		if v == nil {
			return ""
		}
		switch val := v.(type) {
		case string:
			return val
		case []byte:
			return string(val)
		default:
			return fmt.Sprintf("%v", val)
		}
	}

	// 辅助函数：安全地转换为time.Time
	parseTime := func(v interface{}) time.Time {
		if v == nil {
			return time.Time{}
		}

		// 如果已经是time.Time类型，直接返回
		if t, ok := v.(time.Time); ok {
			return t
		}

		timeStr := parseString(v)

		// 尝试多种时间格式解析
		formats := []string{
			"2006-01-02 15:04:05",
			time.RFC3339,
			"2006-01-02T15:04:05Z",
			"2006-01-02",
		}

		for _, format := range formats {
			if t, err := time.Parse(format, timeStr); err == nil {
				return t
			}
		}

		// 尝试解析Unix时间戳
		if i, err := strconv.ParseInt(timeStr, 10, 64); err == nil {
			return time.Unix(i, 0)
		}

		return time.Time{}
	}

	// 映射所有字段
	if v, ok := data["id"]; ok {
		evaluation.ID = parseInt64(v)
	}
	if v, ok := data["evaluation_id"]; ok {
		evaluation.EvaluationID = parseString(v)
	}
	if v, ok := data["customer_id"]; ok {
		evaluation.CustomerID = parseInt(v)
	}
	if v, ok := data["risk_score"]; ok {
		evaluation.RiskScore = parseInt(v)
	}
	if v, ok := data["risk_result"]; ok {
		evaluation.RiskResult = parseInt(v)
	}

	if v, ok := data["evaluation_time"]; ok {
		evaluation.EvaluationTime = parseTime(v)
	}
	if v, ok := data["created_at"]; ok {
		evaluation.CreatedAt = parseTime(v)
	}
	if v, ok := data["updated_at"]; ok {
		evaluation.UpdatedAt = parseTime(v)
	}

	return nil
}

// mapToRiskRawData 专门用于将数据映射到RiskRawData结构体，处理类型转换
func mapToRiskRawData(data gform.Data, rawData *RiskRawData) error {
	if rawData == nil {
		return fmt.Errorf("目标结构体不能为空")
	}

	// 辅助函数：安全地转换为int64
	parseInt64 := func(v interface{}) int64 {
		if v == nil {
			return 0
		}
		switch val := v.(type) {
		case int64:
			return val
		case int:
			return int64(val)
		case float64:
			return int64(val)
		case string:
			if i, err := strconv.ParseInt(val, 10, 64); err == nil {
				return i
			}
		}
		return 0
	}

	// 辅助函数：安全地转换为string
	parseString := func(v interface{}) string {
		if v == nil {
			return ""
		}
		switch val := v.(type) {
		case string:
			return val
		case []byte:
			return string(val)
		default:
			return fmt.Sprintf("%v", val)
		}
	}

	// 辅助函数：安全地转换为time.Time
	parseTime := func(v interface{}) time.Time {
		if v == nil {
			return time.Time{}
		}
		timeStr := parseString(v)

		// 尝试多种时间格式解析
		formats := []string{
			"2006-01-02 15:04:05",
			time.RFC3339,
			"2006-01-02T15:04:05Z",
			"2006-01-02",
		}

		for _, format := range formats {
			if t, err := time.Parse(format, timeStr); err == nil {
				return t
			}
		}

		// 尝试解析Unix时间戳
		if i, err := strconv.ParseInt(timeStr, 10, 64); err == nil {
			return time.Unix(i, 0)
		}

		return time.Time{}
	}

	// 映射所有字段
	if v, ok := data["id"]; ok {
		rawData.ID = parseInt64(v)
	}
	if v, ok := data["evaluation_id"]; ok {
		rawData.EvaluationID = parseString(v)
	}
	if v, ok := data["leida_v4_response"]; ok {
		rawData.LeidaV4Response = parseString(v)
	}
	if v, ok := data["tan_zhen_c_response"]; ok {
		rawData.TanZhenCResponse = parseString(v)
	}
	if v, ok := data["data_source"]; ok {
		rawData.DataSource = parseString(v)
	}
	if v, ok := data["created_at"]; ok {
		rawData.CreatedAt = parseTime(v)
	}
	if v, ok := data["updated_at"]; ok {
		rawData.UpdatedAt = parseTime(v)
	}

	return nil
}
