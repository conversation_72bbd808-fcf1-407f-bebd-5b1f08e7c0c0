package riskmodelservice

import (
	"encoding/json"
	"fincore/global"
	"fincore/model"
	"fincore/thirdparty/riskthirdparty"
	"io/ioutil"
	"testing"

	"go.uber.org/zap"
)

// TestRiskModelService 测试风控模型服务
func TestRiskModelService(t *testing.T) {

	// 创建风控模型服务实例
	riskModelService := NewRiskModelService()
	thirdPartyService := riskthirdparty.NewRiskThirdPartyService()
	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 跳过数据库初始化，使用模拟数据
	model.MyInit("test") // 注释掉数据库初始化，避免配置依赖
	// 测试第三方数据合并功能
	t.Run("TestCombineThirdPartyData", func(t *testing.T) {

		tanZhenData, err := riskModelService.getThirdPartyData(thirdPartyService, "tan_zhen_c", &riskthirdparty.RiskRequest{
			Name:   "李四",                 // 这里应该从客户信息中获取
			IDNo:   "110102200101017072", // 这里应该从客户信息中获取
			Mobile: "13866666666",        // 这里应该从客户信息中获取
		})
		if err != nil {
			t.Errorf("获取第三方数据失败: %v", err)
		}

		if tanZhenData == nil {
			t.Error("获取第三方数据失败")
		}

		// 模拟leida_v4数据
		leidaData, err := riskModelService.getThirdPartyData(thirdPartyService, "leida_v4", &riskthirdparty.RiskRequest{
			Name:   "李四",                 // 这里应该从客户信息中获取
			IDNo:   "110102200101017072", // 这里应该从客户信息中获取
			Mobile: "13866666666",        // 这里应该从客户信息中获取
		})
		if err != nil {
			t.Errorf("获取第三方数据失败: %v", err)
		}

		// 测试数据合并
		combinedData := riskModelService.combineThirdPartyData(tanZhenData, leidaData)

		request := &RCSRequest{
			TanZhenC: combinedData["tan_zhen_c"],
			LeidaV4:  combinedData["leida_v4"],
		}

		// 打印 request 以json 的格式并写入到当前路径下

		requestJSON, err := json.Marshal(request)
		if err != nil {
			t.Errorf("序列化请求失败: %v", err)
		}
		t.Logf("request: %s", requestJSON)
		// 写入文件 为什么会有 \ 符号存在

		err = ioutil.WriteFile("request.json", requestJSON, 0644)
		if err != nil {
			t.Errorf("写入文件失败: %v", err)
		}

		// 打印
		// 验证合并结果
		if combinedData == nil {
			t.Error("合并数据为空")
			return
		}

		// 验证合并数据包含两个产品的信息
		if combinedData["tan_zhen_c"] == nil {
			t.Error("合并数据中缺少tan_zhen_c信息")
		}

		if combinedData["leida_v4"] == nil {
			t.Error("合并数据中缺少leida_v4信息")
		}

		// 验证合并标识
		if combinedData["data_source"] != "combined_thirdparty" {
			t.Error("缺少合并标识")
		}

		// 验证时间戳
		if combinedData["merge_time"] == nil {
			t.Error("缺少合并时间戳")
		}

		// t.Logf("合并数据验证成功: %+v", combinedData)
	})
}

// TestRiskServiceIntegration 测试风控服务集成
func TestRiskServiceIntegration(t *testing.T) {
	// 这里需要模拟依赖服务
	// 在实际测试中，应该使用mock对象

	t.Run("TestMergeRiskResults", func(t *testing.T) {
		// 创建风控服务实例
		// 测试新模型结果
		newResult := &RiskEvaluationResult{
			FinalScore:  720,
			FinalResult: APPROVED,
			CreditLimit: &CreditLimitResponse{
				Code:        200,
				Message:     "成功",
				CreditLimit: "80000",
			},
		}

		if newResult.FinalScore != 720 {
			t.Errorf("合并后评分错误，期望720，实际%d", newResult.FinalScore)
		}

		if newResult.FinalResult != APPROVED {
			t.Errorf("合并后结果错误，期望APPROVED，实际%s", newResult.FinalResult)
		}

		if newResult.CreditLimit.Code != 200 {
			t.Errorf("合并后授信额度错误，期望200，实际%d", newResult.CreditLimit.Code)
		}

		t.Logf("合并结果: %+v", newResult)
	})

	t.Run("TestMergeRiskResultsWithFailedNewModel", func(t *testing.T) {

	})
}
