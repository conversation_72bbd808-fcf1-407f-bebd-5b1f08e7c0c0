package risk

import (
	"context"
	"reflect"
	"time"

	"fincore/app/business/risk"
	"fincore/global"
	"fincore/model"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"
	"fincore/utils/utilstool/goredis"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RiskController 风控控制器
type RiskController struct{}

func init() {
	// 注册路由到自动路由系统
	gf.Register(&RiskController{}, reflect.TypeOf(RiskController{}).PkgPath())
}

// GetEvaluate 授信评估接口
// 路由: GET /uniapp/risk/riskcontroller/getEvaluate
func (rc *RiskController) GetEvaluate(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
		"channel_id":  c.<PERSON><PERSON>("channel_id"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := risk.GetRiskEvaluationSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 优先从缓存获取风控评估结果
	result, err := risk.GetRiskService().GetCachedRiskEvaluation(ctx, validationResult.Data)
	if err != nil {
		// 缓存未命中或失败，先从数据库读取最新评估记录
		customerID, convErr := risk.GetRiskService().GetCustomerIDFromData(validationResult.Data)
		if convErr != nil {
			results.Failed(c, "参数转换失败", convErr.Error())
			return
		}
		//获取渠道ID
		channelID, convErr := risk.GetRiskService().GetChannelIDFromData(validationResult.Data)
		if convErr != nil {
			results.Failed(c, "参数转换失败", convErr.Error())
			return
		}
		riskEvalService := model.NewRiskEvaluationService()
		// todo 还是从后端读取channelid
		latestEvaluation, dbErr := riskEvalService.GetLatestEvaluationByCustomerID(int(customerID), int(channelID))
		if dbErr == nil && latestEvaluation != nil {
			// 检查评估时间是否超过一天
			now := time.Now()
			timeDiff := now.Sub(latestEvaluation.EvaluationTime)
			if timeDiff < 24*time.Hour {
				// 评估时间未超过一天，使用数据库记录
				result = &risk.RiskEvaluationResponse{
					RiskReportID:   latestEvaluation.EvaluationID,
					RiskScore:      latestEvaluation.RiskScore,
					RiskResult:     latestEvaluation.RiskResult,
					EvaluationTime: latestEvaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
				}

				// 重新设置缓存，计算剩余过期时间
				cacheData := map[string]interface{}{
					"risk_score":      latestEvaluation.RiskScore,
					"risk_result":     float64(latestEvaluation.RiskResult),
					"evaluation_id":   latestEvaluation.EvaluationID,
					"evaluation_time": latestEvaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
				}
				remainingTime := 24*time.Hour - timeDiff
				if setErr := goredis.SetRiskScore(customerID, cacheData, remainingTime); setErr != nil {
					global.App.Log.Error("重新设置风控缓存失败", zap.Error(setErr), zap.Uint64("customer_id", customerID))
				}
			} else {
				// 评估时间超过一天，重新评估
				result, err = risk.GetRiskService().EvaluateRisk(ctx, validationResult.Data)
				if err != nil {
					results.Failed(c, "风控评估失败", err.Error())
					return
				}
			}
		} else {
			// 数据库中没有记录，进行新的风控评估
			result, err = risk.GetRiskService().EvaluateRisk(ctx, validationResult.Data)
			if err != nil {
				results.Failed(c, "风控评估失败", err.Error())
				return
			}
		}
	}

	// 3. 返回结果
	results.Success(c, "评估成功", result, nil)
}

// GetProducts 贷款产品匹配接口
// 路由: GET /uniapp/risk/riskcontroller/getProducts
func (rc *RiskController) GetProducts(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
		"channel_id":  c.Query("channel_id"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := risk.GetLoanProductsSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	// 从数据库获取 idcard
	// 1. 参数转换
	customerID, err := risk.GetRiskService().GetCustomerIDFromData(validationResult.Data)
	if err != nil {
		results.Failed(c, "参数转换失败", err.Error())
		return
	}
	customerInfo, err := risk.GetRiskService().GetCustomerInfo(ctx, int64(customerID))
	if err != nil {
		results.Failed(c, "获取客户信息失败", err.Error())
		return
	}

	noCardResult := &risk.LoanProductsResponse{
		OverallCreditLimit: 50000,
	}
	// 验证客户信息完整性
	if customerInfo.Name == "" || customerInfo.IDCard == "" || customerInfo.Mobile == "" {
		results.Success(c, "获取成功", noCardResult, nil)
		return
	}

	result, err := risk.GetRiskService().GetLoanProducts(ctx, validationResult.Data)
	if err != nil {
		results.Failed(c, "获取产品列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "获取成功", result, nil)
}
