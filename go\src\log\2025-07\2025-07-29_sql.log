{"level":"dev.warn","ts":"[2025-07-29 17:07:49.616]","caller":"model/sql_logger_wrapper.go:72","msg":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t\t\tblo.audit_assignee_id, blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at, blo.completed_at, blo.contract_id,\n\t\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\t\tc2.channel_name as initial_order_channel_name,\n\t\t\tpr.loan_period as loan_period, pr.total_periods as total_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id ORDER BY blo.id DESC LIMIT 20, []","request_id":"1856adae6d98d7d4d836c68f","runtime":"31.8238ms","type":"SLOW","duration_ms":31,"is_slow":false,"threshold_exceeded":false}
{"level":"dev.info","ts":"[2025-07-29 17:07:49.617]","caller":"model/sql_logger_wrapper.go:59","msg":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t\t\tblo.audit_assignee_id, blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at, blo.completed_at, blo.contract_id,\n\t\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\t\tc2.channel_name as initial_order_channel_name,\n\t\t\tpr.loan_period as loan_period, pr.total_periods as total_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id ORDER BY blo.id DESC LIMIT 20, []","request_id":"1856adae6d98d7d4d836c68f","runtime":"31.8238ms","type":"SQL","duration_ms":31}
{"level":"dev.warn","ts":"[2025-07-29 17:07:49.652]","caller":"model/sql_logger_wrapper.go:72","msg":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id LIMIT 1, []","request_id":"1856adae6d98d7d4d836c68f","runtime":"67.4164ms","type":"SLOW","duration_ms":67,"is_slow":false,"threshold_exceeded":false}
{"level":"dev.info","ts":"[2025-07-29 17:07:49.652]","caller":"model/sql_logger_wrapper.go:59","msg":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id LIMIT 1, []","request_id":"1856adae6d98d7d4d836c68f","runtime":"67.4164ms","type":"SQL","duration_ms":67}
{"level":"dev.warn","ts":"[2025-07-29 17:07:49.666]","caller":"model/sql_logger_wrapper.go:72","msg":"SELECT id, name FROM `business_account` WHERE `id` in (?,?), [1 2]","request_id":"1856adae6d98d7d4d836c68f","runtime":"14.4228ms","type":"SLOW","duration_ms":14,"is_slow":false,"threshold_exceeded":false}
{"level":"dev.info","ts":"[2025-07-29 17:07:49.666]","caller":"model/sql_logger_wrapper.go:59","msg":"SELECT id, name FROM `business_account` WHERE `id` in (?,?), [1 2]","request_id":"1856adae6d98d7d4d836c68f","runtime":"14.4228ms","type":"SQL","duration_ms":14}
{"level":"dev.warn","ts":"[2025-07-29 17:07:49.666]","caller":"model/sql_logger_wrapper.go:72","msg":"SELECT order_id, COUNT(*) as paid_count FROM `business_repayment_bills` WHERE `order_id` in (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) and `status` = ? GROUP BY order_id, [40 39 38 37 36 35 34 33 32 31 28 27 26 25 24 23 21 20 19 18 1]","request_id":"1856adae6d98d7d4d836c68f","runtime":"14.4228ms","type":"SLOW","duration_ms":14,"is_slow":false,"threshold_exceeded":false}
{"level":"dev.info","ts":"[2025-07-29 17:07:49.666]","caller":"model/sql_logger_wrapper.go:59","msg":"SELECT order_id, COUNT(*) as paid_count FROM `business_repayment_bills` WHERE `order_id` in (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) and `status` = ? GROUP BY order_id, [40 39 38 37 36 35 34 33 32 31 28 27 26 25 24 23 21 20 19 18 1]","request_id":"1856adae6d98d7d4d836c68f","runtime":"14.4228ms","type":"SQL","duration_ms":14}
{"level":"dev.warn","ts":"[2025-07-29 17:07:49.676]","caller":"model/sql_logger_wrapper.go:72","msg":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (86,84,82,81,75,63,61,60,58,53,27,26,25)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","request_id":"1856adae6d98d7d4d836c68f","runtime":"10.0545ms","type":"SLOW","duration_ms":10,"is_slow":false,"threshold_exceeded":false}
{"level":"dev.info","ts":"[2025-07-29 17:07:49.676]","caller":"model/sql_logger_wrapper.go:59","msg":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (86,84,82,81,75,63,61,60,58,53,27,26,25)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","request_id":"1856adae6d98d7d4d836c68f","runtime":"10.0545ms","type":"SQL","duration_ms":10}
{"level":"dev.warn","ts":"[2025-07-29 17:08:35.090]","caller":"model/sql_logger_wrapper.go:72","msg":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id LIMIT 1, []","request_id":"1856adb904305794d7ed090f","runtime":"32.3494ms","type":"SLOW","duration_ms":32,"is_slow":false,"threshold_exceeded":false}
{"level":"dev.info","ts":"[2025-07-29 17:08:35.090]","caller":"model/sql_logger_wrapper.go:59","msg":"SELECT count(*) as count FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id LIMIT 1, []","request_id":"1856adb904305794d7ed090f","runtime":"32.3494ms","type":"SQL","duration_ms":32}
{"level":"dev.warn","ts":"[2025-07-29 17:08:35.091]","caller":"model/sql_logger_wrapper.go:72","msg":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t\t\tblo.audit_assignee_id, blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at, blo.completed_at, blo.contract_id,\n\t\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\t\tc2.channel_name as initial_order_channel_name,\n\t\t\tpr.loan_period as loan_period, pr.total_periods as total_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id ORDER BY blo.id DESC LIMIT 20, []","request_id":"1856adb904305794d7ed090f","runtime":"33.2702ms","type":"SLOW","duration_ms":33,"is_slow":false,"threshold_exceeded":false}
{"level":"dev.info","ts":"[2025-07-29 17:08:35.091]","caller":"model/sql_logger_wrapper.go:59","msg":"SELECT blo.id, blo.order_no, blo.user_id, blo.product_rule_id, blo.loan_amount,\n\t\t\tblo.principal, blo.total_interest, blo.total_guarantee_fee, blo.total_other_fees,\n\t\t\tblo.total_repayable_amount, blo.amount_paid, blo.channel_id, blo.customer_origin,\n\t\t\tblo.initial_order_channel_id, blo.status, blo.is_freeze, baa.complaintStatus as complaint_status,\n\t\t\tblo.audit_assignee_id, blo.review_status, blo.sales_assignee_id, blo.collection_assignee_id,\n\t\t\tblo.reason_for_closure, blo.created_at, blo.updated_at, blo.disbursed_at, blo.completed_at, blo.contract_id,\n\t\t\tbaa.name as user_name, baa.mobile as user_mobile, baa.idCard as user_id_card, baa.repeatBuyNum as repeat_buy_num,\n\t\t\tc1.channel_name as channel_name, bpc.channel_name as payment_channel_name,\n\t\t\tc2.channel_name as initial_order_channel_name,\n\t\t\tpr.loan_period as loan_period, pr.total_periods as total_periods FROM business_loan_orders blo LEFT JOIN business_app_account baa ON blo.user_id = baa.id  LEFT JOIN channel c1 ON blo.channel_id = c1.id  LEFT JOIN channel c2 ON baa.channelId = c2.id  LEFT JOIN product_rules pr ON blo.product_rule_id = pr.id  LEFT JOIN business_payment_channels bpc ON blo.payment_channel_id = bpc.id ORDER BY blo.id DESC LIMIT 20, []","request_id":"1856adb904305794d7ed090f","runtime":"33.2702ms","type":"SQL","duration_ms":33}
{"level":"dev.warn","ts":"[2025-07-29 17:08:35.102]","caller":"model/sql_logger_wrapper.go:72","msg":"SELECT order_id, COUNT(*) as paid_count FROM `business_repayment_bills` WHERE `order_id` in (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) and `status` = ? GROUP BY order_id, [40 39 38 37 36 35 34 33 32 31 28 27 26 25 24 23 21 20 19 18 1]","request_id":"1856adb904305794d7ed090f","runtime":"10.8018ms","type":"SLOW","duration_ms":10,"is_slow":false,"threshold_exceeded":false}
{"level":"dev.info","ts":"[2025-07-29 17:08:35.102]","caller":"model/sql_logger_wrapper.go:59","msg":"SELECT order_id, COUNT(*) as paid_count FROM `business_repayment_bills` WHERE `order_id` in (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?) and `status` = ? GROUP BY order_id, [40 39 38 37 36 35 34 33 32 31 28 27 26 25 24 23 21 20 19 18 1]","request_id":"1856adb904305794d7ed090f","runtime":"10.8018ms","type":"SQL","duration_ms":10}
{"level":"dev.warn","ts":"[2025-07-29 17:08:35.102]","caller":"model/sql_logger_wrapper.go:72","msg":"SELECT id, name FROM `business_account` WHERE `id` in (?,?), [2 1]","request_id":"1856adb904305794d7ed090f","runtime":"10.8018ms","type":"SLOW","duration_ms":10,"is_slow":false,"threshold_exceeded":false}
{"level":"dev.info","ts":"[2025-07-29 17:08:35.102]","caller":"model/sql_logger_wrapper.go:59","msg":"SELECT id, name FROM `business_account` WHERE `id` in (?,?), [2 1]","request_id":"1856adb904305794d7ed090f","runtime":"10.8018ms","type":"SQL","duration_ms":10}
{"level":"dev.warn","ts":"[2025-07-29 17:08:35.114]","caller":"model/sql_logger_wrapper.go:72","msg":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (86,84,82,81,75,63,61,60,58,53,27,26,25)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","request_id":"1856adb904305794d7ed090f","runtime":"11.4808ms","type":"SLOW","duration_ms":11,"is_slow":false,"threshold_exceeded":false}
{"level":"dev.info","ts":"[2025-07-29 17:08:35.114]","caller":"model/sql_logger_wrapper.go:59","msg":"\n\t\tSELECT re.customer_id, re.risk_score\n\t\tFROM risk_evaluations re\n\t\tINNER JOIN (\n\t\t\tSELECT customer_id, MAX(created_at) as latest_time\n\t\t\tFROM risk_evaluations\n\t\t\tWHERE customer_id IN (86,84,82,81,75,63,61,60,58,53,27,26,25)\n\t\t\tGROUP BY customer_id\n\t\t) latest ON re.customer_id = latest.customer_id AND re.created_at = latest.latest_time\n\t, []","request_id":"1856adb904305794d7ed090f","runtime":"11.4808ms","type":"SQL","duration_ms":11}
