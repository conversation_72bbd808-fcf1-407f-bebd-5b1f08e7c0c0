package common

import (
	"encoding/json"
	"fincore/global"
	"fincore/utils/gf"
	"fmt"
	"io"
	"math/rand"
	"net/http"
	"strings"
	"time"
)

/*
这里后续可以添加模板
1： 短信验证码
2： ...
3:  ...
如果后续验证码模板较多且需要动态管理可使用数据库存储
*/
var smsTemplates = map[int]string{
	1: "您的验证码是：%s。请在10分钟内输入。如非本人操作，请忽略本短信。",
	2: "Hello World!",
}

var (
	// 短信平台账号
	account string
	// 短信平台密码
	password string
	// 短信平台签名
	sign string
	// 短信平台地址
	apiurl string
)

// 定义短信请求的数据结构
type smsRequest struct {
	Account  string `json:"account"`
	Password string `json:"password"`
	Data     []struct {
		Msgid    string `json:"msgid"`
		Phones   string `json:"phones"`
		Content  string `json:"content"`
		Sign     string `json:"sign"`
		Subcode  string `json:"subcode"`
		Sendtime string `json:"sendtime"`
	} `json:"data"`
}

func SendSmsApi(mobile string, smsCode string, template int) error {
	// 入参校验
	if len(mobile) != 11 {
		return fmt.Errorf("手机号格式不正确")
	}
	if len(smsCode) != 6 {
		return fmt.Errorf("验证码长度不正确")
	}
	if template < 1 || template > len(smsTemplates) {
		return fmt.Errorf("模板不存在")
	}
	// 验证手机号格式
	mobilePattern := `^1[3-9]\d{9}$`
	if !gf.MatchRegex(mobile, mobilePattern) {
		return fmt.Errorf("手机号格式不正确")
	}
	// 验证验证码格式
	fmt.Println("验证码格式:", smsCode)
	codePattern := `^\d{6}$`
	if !gf.MatchRegex(smsCode, codePattern) {
		return fmt.Errorf("验证码格式不正确")
	}
	account = global.App.Config.Sms.Account
	password = global.App.Config.Sms.Password
	sign = global.App.Config.Sms.Sign
	apiurl = global.App.Config.Sms.Apiurl
	// 根据template的值选择不同的短信模板
	content, ok := smsTemplates[template]
	if !ok {
		return fmt.Errorf("未找到对应的短信模板: %d", template)
	}
	if template == 1 {
		// 模板编码为1 即发送生成的验证码
		// 验证码填充至模板中
		content = fmt.Sprintf(content, smsCode)
	}

	// msgid 随机生成
	const letters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	rand.Seed(time.Now().UnixNano())
	b := make([]byte, 10)
	for i := range b {
		b[i] = letters[rand.Intn(len(letters))]
	}
	msgid := string(b)

	// 构造请求数据结构
	reqData := smsRequest{
		Account:  account,
		Password: password,
		Data: []struct {
			Msgid    string `json:"msgid"`
			Phones   string `json:"phones"`
			Content  string `json:"content"`
			Sign     string `json:"sign"`
			Subcode  string `json:"subcode"`
			Sendtime string `json:"sendtime"`
		}{
			{
				Msgid:    msgid,
				Phones:   mobile,
				Content:  content,
				Sign:     sign,
				Subcode:  "",
				Sendtime: "",
			},
		},
	}

	// 将请求数据序列化为JSON
	reqBody, err := json.Marshal(reqData)
	if err != nil {
		return fmt.Errorf("JSON序列化失败: %w", err)
	}

	// 创建请求
	req, err := http.NewRequest("POST", apiurl, strings.NewReader(string(reqBody)))
	if err != nil {
		return fmt.Errorf("创建请求失败: %w", err)
	}

	// 设置请求头
	req.Header.Set("Content-Type", "application/json")

	// 发送HTTP请求
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("发送请求失败: %w", err)
	}
	defer resp.Body.Close()

	// 读取响应
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("读取响应失败: %w", err)
	}

	// 解析响应
	var result map[string]interface{}
	err = json.Unmarshal(body, &result)
	if err != nil {
		return fmt.Errorf("解析响应JSON失败: %w", err)
	}
	if result["result"] == "0" {
		return nil
	} else {
		msg, _ := result["desc"].(string)
		return fmt.Errorf("发送失败: %s", msg)
	}
}
