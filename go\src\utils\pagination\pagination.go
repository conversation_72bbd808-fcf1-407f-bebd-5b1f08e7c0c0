package pagination

import (
	"fmt"
	"math"
	"strconv"

	"fincore/utils/gform"
)

// PaginationRequest 分页请求参数
type PaginationRequest struct {
	Page     int `json:"page" form:"page"`         // 当前页码，从1开始
	PageSize int `json:"pageSize" form:"pageSize"` // 每页数量
}

// PaginationResponse 分页响应数据
type PaginationResponse struct {
	Total      int64       `json:"total"`      // 总记录数
	Page       int         `json:"page"`       // 当前页码
	PageSize   int         `json:"pageSize"`   // 每页数量
	TotalPages int         `json:"totalPages"` // 总页数
	HasNext    bool        `json:"hasNext"`    // 是否有下一页
	HasPrev    bool        `json:"hasPrev"`    // 是否有上一页
	Data       interface{} `json:"data"`       // 数据列表
}

// DefaultPageSize 默认每页数量
const DefaultPageSize = 10

// MaxPageSize 最大每页数量
const MaxPageSize = 1000

// ValidateAndNormalize 验证和标准化分页参数
func (p *PaginationRequest) ValidateAndNormalize() {
	// 页码默认为1
	if p.Page <= 0 {
		p.Page = 1
	}

	// 每页数量默认为10
	if p.PageSize <= 0 {
		p.PageSize = DefaultPageSize
	}

	// 限制最大每页数量
	if p.PageSize > MaxPageSize {
		p.PageSize = MaxPageSize
	}
}

// GetOffset 获取数据库查询的偏移量
func (p *PaginationRequest) GetOffset() int {
	return (p.Page - 1) * p.PageSize
}

// GetLimit 获取数据库查询的限制数量
func (p *PaginationRequest) GetLimit() int {
	return p.PageSize
}

// Paginate 执行分页查询
func Paginate(orm gform.IOrm, req PaginationRequest) (*PaginationResponse, error) {
	// 验证和标准化参数
	req.ValidateAndNormalize()

	// 保存原始的fields设置，因为Count()会修改它
	originalFields := orm.GetFields()

	// 获取总记录数
	total, err := orm.Count()
	if err != nil {
		return nil, fmt.Errorf("获取总记录数失败: %v", err)
	}

	// 恢复原始的fields设置
	if len(originalFields) > 0 {
		orm.Fields(originalFields...)
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	// 如果当前页超过总页数，调整为最后一页
	if req.Page > totalPages && totalPages > 0 {
		req.Page = totalPages
	}

	// 执行分页查询
	data, err := orm.Offset(req.GetOffset()).Limit(req.GetLimit()).Get()
	if err != nil {
		return nil, fmt.Errorf("执行分页查询失败: %v", err)
	}

	// 构建响应
	response := &PaginationResponse{
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
		HasNext:    req.Page < totalPages,
		HasPrev:    req.Page > 1,
		Data:       data,
	}

	return response, nil
}

// PaginateWithCustomQuery 使用自定义查询进行分页
func PaginateWithCustomQuery(
	countOrm gform.IOrm, // 用于计算总数的ORM
	dataOrm gform.IOrm, // 用于查询数据的ORM
	req PaginationRequest,
) (*PaginationResponse, error) {
	// 验证和标准化参数
	req.ValidateAndNormalize()

	// 执行分页查询
	data, err := dataOrm.Offset(req.GetOffset()).Limit(req.GetLimit()).Get()
	if err != nil {
		return nil, fmt.Errorf("执行分页查询失败: %v", err)
	}

	// 获取总记录数
	total, err := countOrm.Count()
	if err != nil {
		return nil, fmt.Errorf("获取总记录数失败: %v", err)
	}

	// 计算总页数
	totalPages := int(math.Ceil(float64(total) / float64(req.PageSize)))

	// 如果当前页超过总页数，调整为最后一页
	if req.Page > totalPages && totalPages > 0 {
		req.Page = totalPages
	}

	// 构建响应
	response := &PaginationResponse{
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
		HasNext:    req.Page < totalPages,
		HasPrev:    req.Page > 1,
		Data:       data,
	}

	return response, nil
}

// ParsePaginationFromQuery 从查询参数解析分页参数
func ParsePaginationFromQuery(pageStr, pageSizeStr string) PaginationRequest {
	req := PaginationRequest{}

	if pageStr != "" {
		if page, err := strconv.Atoi(pageStr); err == nil {
			req.Page = page
		}
	}

	if pageSizeStr != "" {
		if pageSize, err := strconv.Atoi(pageSizeStr); err == nil {
			req.PageSize = pageSize
		}
	}

	req.ValidateAndNormalize()
	return req
}

// BuildPaginationInfo 构建分页信息
func BuildPaginationInfo(total int64, page, pageSize int) map[string]interface{} {
	totalPages := int(math.Ceil(float64(total) / float64(pageSize)))

	return map[string]interface{}{
		"total":      total,
		"page":       page,
		"pageSize":   pageSize,
		"totalPages": totalPages,
		"hasNext":    page < totalPages,
		"hasPrev":    page > 1,
	}
}
