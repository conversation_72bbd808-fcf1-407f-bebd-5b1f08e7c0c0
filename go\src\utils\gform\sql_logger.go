package gform

import (
	"context"
	"fincore/utils/log"
	"time"
)

// SqlLogger SQL日志适配器，实现ILogger接口
type SqlLogger struct {
	logger *log.Logger
	env    string
}

// NewSqlLogger 创建SQL日志适配器
func NewSqlLogger(env string) *SqlLogger {
	return &SqlLogger{
		logger: log.SQL(),
		env:    env,
	}
}

// EnableSqlLog dev环境记录所有SQL，prod环境不记录
func (l *SqlLogger) EnableSqlLog() bool {
	return l.env == "dev"
}

// EnableSlowLog dev环境不需要（避免重复），prod环境记录超过2秒的查询
func (l *SqlLogger) EnableSlowLog() float64 {
	if l.env == "dev" {
		return 0 // dev环境不记录慢查询，避免与SQL日志重复
	}
	return 2.0
}

// EnableErrorLog 所有环境都记录错误
func (l *SqlLogger) EnableErrorLog() bool {
	return true
}

// Sql 记录SQL执行日志
func (l *SqlLogger) Sql(sqlStr string, runtime time.Duration) {
	if l.EnableSqlLog() {
		l.logger.Info("SQL执行",
			log.String("sql", sqlStr),
			log.Duration("duration", runtime),
		)
	}
}

// Slow 记录慢查询日志（只在不记录所有SQL时才记录，避免重复）
func (l *SqlLogger) Slow(sqlStr string, runtime time.Duration) {
	if !l.EnableSqlLog() && l.EnableSlowLog() > 0 && runtime.Seconds() > l.EnableSlowLog() {
		l.logger.Warn("慢查询",
			log.String("sql", sqlStr),
			log.Duration("duration", runtime),
		)
	}
}

// Error 记录错误日志
func (l *SqlLogger) Error(msg string) {
	if l.EnableErrorLog() {
		l.logger.Error("SQL错误", log.String("error", msg))
	}
}

// SqlLoggerWithContext 带上下文的SQL日志适配器
type SqlLoggerWithContext struct {
	*SqlLogger
	ctx context.Context
}

// NewSqlLoggerWithContext 创建带上下文的SQL日志适配器
func NewSqlLoggerWithContext(env string, ctx context.Context) *SqlLoggerWithContext {
	return &SqlLoggerWithContext{
		SqlLogger: NewSqlLogger(env),
		ctx:       ctx,
	}
}

// Sql 记录带请求ID的SQL执行日志
func (l *SqlLoggerWithContext) Sql(sqlStr string, runtime time.Duration) {
	if l.EnableSqlLog() {
		logger := l.logger
		if requestID := log.GetRequestIDFromContext(l.ctx); requestID != "" {
			logger = logger.WithRequestID(requestID)
		}
		logger.Info("SQL执行",
			log.String("sql", sqlStr),
			log.Duration("duration", runtime),
		)
	}
}

// Slow 记录带请求ID的慢查询日志
func (l *SqlLoggerWithContext) Slow(sqlStr string, runtime time.Duration) {
	if !l.EnableSqlLog() && l.EnableSlowLog() > 0 && runtime.Seconds() > l.EnableSlowLog() {
		logger := l.logger
		if requestID := log.GetRequestIDFromContext(l.ctx); requestID != "" {
			logger = logger.WithRequestID(requestID)
		}
		logger.Warn("慢查询",
			log.String("sql", sqlStr),
			log.Duration("duration", runtime),
		)
	}
}

// Error 记录带请求ID的错误日志
func (l *SqlLoggerWithContext) Error(msg string) {
	if l.EnableErrorLog() {
		logger := l.logger
		if requestID := log.GetRequestIDFromContext(l.ctx); requestID != "" {
			logger = logger.WithRequestID(requestID)
		}
		logger.Error("SQL错误", log.String("error", msg))
	}
}
