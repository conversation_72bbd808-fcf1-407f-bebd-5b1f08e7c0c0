package scheduler

import (
	"fincore/app/scheduler/tasks/orders"
	"fincore/app/scheduler/tasks/repayment"
	// "fincore/app/scheduler/tasks/examples"
)

// InitializeTasks 初始化任务
func InitializeTasks() {
	RegisterTasks(
		orders.NewAutoDisbursementCompensationTask(),
		orders.NewDisbursementStatusSyncCompensationTask(),
		orders.NewRefundStatusSyncCompensationTask(),
		repayment.NewAutoWithholdTask(),
	// examples.NewHelloTimeTask(),
	)
}
