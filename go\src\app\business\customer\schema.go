package customer

import (
	"fincore/utils/jsonschema"
)

// floatPtr 返回float64指针，用于jsonschema的Min/Max字段
func floatPtr(f float64) *float64 {
	return &f
}

// GetCustomerListSchema 客户列表查询参数验证规则
func GetCustomerListSchema() jsonschema.Schema {
	minVal := 0.0
	maxVal := 999999999.99

	return jsonschema.Schema{
		Title: "客户列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			// 基础信息
			"name": {
				Type:        "string",
				MaxLength:   50,
				Description: "姓名",
			},
			"mobile": {
				Type:        "string",
				Pattern:     "^1[3-9]\\d{9}$",
				Description: "手机号码",
			},
			"idCard": {
				Type:        "string",
				Pattern:     "^[1-9]\\d{5}(18|19|20)\\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\\d{3}[0-9Xx]$",
				Description: "身份证号码",
			},
			"reviewerID": {
				Type:        "string",
				Description: "审核人ID",
			},
			"channelId": {
				Type:        "string",
				Description: "渠道ID",
			},

			// 风控相关
			"isRisk": {
				Type:        "string",
				Enum:        []string{"0", "1"},
				Description: "是否风控：0-否，1-是",
			},
			"riskFlowNumber": {
				Type:        "string",
				MaxLength:   100,
				Description: "风控流水号",
			},
			"riskScoreMin": {
				Type:        "number",
				Min:         &minVal,
				Max:         &maxVal,
				Description: "风控分数最小值",
			},
			"riskScoreMax": {
				Type:        "number",
				Min:         &minVal,
				Max:         &maxVal,
				Description: "风控分数最大值",
			},

			// 额度相关
			"reminderQuotaMin": {
				Type:        "number",
				Min:         &minVal,
				Max:         &maxVal,
				Description: "剩余额度最小值",
			},
			"reminderQuotaMax": {
				Type:        "number",
				Min:         &minVal,
				Max:         &maxVal,
				Description: "剩余额度最大值",
			},

			// 状态相关
			"identityStatus": {
				Type:        "string",
				Description: "认证状态阶段：1-申请前，2-等待申请结果，3-申请通过，4-申请不通过",
			},
			"identitySubStatus": {
				Type:        "string",
				Description: "认证子状态位值",
			},
			"orderStatus": {
				Type:        "string",
				Enum:        []string{"0", "1", "2", "3"},
				Description: "订单状态：0-无订单，1-进行中，2-已完成，3-已取消",
			},
			"registerIncomplete": {
				Type:        "string",
				Enum:        []string{"1"},
				Description: "注册未实名：1-是",
			},
			"realNameIncomplete": {
				Type:        "string",
				Enum:        []string{"1"},
				Description: "实名未下单：1-是",
			},
			"hasQuotaNoOrder": {
				Type:        "string",
				Enum:        []string{"1"},
				Description: "有额度未下单：1-是",
			},
			"isNewUser": {
				Type:        "string",
				Enum:        []string{"1"},
				Description: "是否新用户：1-是",
			},
			"isComplaint": {
				Type:        "string",
				Enum:        []string{"0", "1"},
				Description: "是否投诉：0-否，1-是",
			},

			// 其他
			"userRemark": {
				Type:        "string",
				MaxLength:   200,
				Description: "用户备注",
			},
			"loanCount": {
				Type:        "number",
				Min:         &minVal,
				Description: "放款次数",
			},
			"deviceSource": {
				Type:        "string",
				Enum:        []string{"h5", "android", "ios", "other"},
				Description: "设备来源",
			},

			// 时间范围
			"loanTimeStart": {
				Type:        "string",
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}:\\d{2})?$",
				Description: "进件开始时间",
			},
			"loanTimeEnd": {
				Type:        "string",
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}:\\d{2})?$",
				Description: "进件结束时间",
			},
			"registerTimeStart": {
				Type:        "string",
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}:\\d{2})?$",
				Description: "注册开始时间",
			},
			"registerTimeEnd": {
				Type:        "string",
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}:\\d{2})?$",
				Description: "注册结束时间",
			},

			// 分页参数
			"page": {
				Type:        "number",
				Default:     1,
				Description: "页码（从1开始）",
			},
			"pageSize": {
				Type:        "number",
				Default:     20,
				Description: "每页数量",
			},
		},
		Required: []string{}, // 所有参数都是可选的
	}
}

// GetCustomerStatusUpdateSchema 客户状态更新参数验证规则
func GetCustomerStatusUpdateSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "客户状态更新参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"id": {
				Type:        "number",
				Min:         floatPtr(1),
				Description: "客户ID",
			},
			"status": {
				Type:        "integer",
				Enum:        []string{"0", "1", "2", "4"},
				Description: "客户状态：0-正常，1-白名单，2-黑名单",
			},
		},
		Required: []string{"id", "status"},
	}
}

// GetCustomerRemarkUpdateSchema 更新客户备注参数验证规则
func GetCustomerRemarkUpdateSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "更新客户备注参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"id": {
				Type:        "number",
				Min:         floatPtr(1),
				Description: "客户ID",
			},
			"userRemark": {
				Type:        "string",
				MaxLength:   500,
				Description: "用户备注内容",
			},
		},
		Required: []string{"id", "userRemark"},
	}
}

// GetCustomerDetailSchema 获取客户详情参数验证规则
func GetCustomerDetailSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "获取客户详情参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"id": {
				Type:        "string",
				Pattern:     "^[1-9]\\d*$",
				Description: "客户ID",
			},
		},
		Required: []string{"id"},
	}
}

// GetCustomerExportSchema 客户数据导出参数验证规则
func GetCustomerExportSchema() jsonschema.Schema {
	// 复用列表查询的验证规则，但移除分页参数
	listSchema := GetCustomerListSchema()

	// 移除分页相关字段
	delete(listSchema.Properties, "page")
	delete(listSchema.Properties, "pageSize")

	listSchema.Title = "客户数据导出参数"

	return listSchema
}

// ======================== 复购客户相关验证规则 ========================

// GetRepurchaseCustomerListSchema 待复购客户列表查询参数验证规则
func GetRepurchaseCustomerListSchema() jsonschema.Schema {
	minVal := 0.0
	maxVal := 999999999.99

	return jsonschema.Schema{
		Title: "待复购客户列表查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			// 基础信息
			"name": {
				Type:        "string",
				MaxLength:   50,
				Description: "姓名",
			},
			"mobile": {
				Type:        "string",
				MaxLength:   11,
				Description: "手机号码（支持部分匹配）",
			},
			"idCard": {
				Type:        "string",
				MaxLength:   18,
				Description: "身份证号码（支持部分匹配）",
			},

			// 渠道和订单信息
			"channelId": {
				Type:        "string",
				Description: "渠道ID",
			},
			"isCancelled": {
				Type:        "string",
				Enum:        []string{"0", "1"},
				Description: "是否注销：0-正常，1-已注销",
			},
			"borrowingOrderCount": {
				Type:        "string",
				Description: "在借订单数",
			},
			"totalOrderCount": {
				Type:        "string",
				Description: "订单总数",
			},

			// 额度信息
			"totalAmountMin": {
				Type:        "number",
				Min:         &minVal,
				Max:         &maxVal,
				Description: "总额度最小值",
			},
			"totalAmountMax": {
				Type:        "number",
				Min:         &minVal,
				Max:         &maxVal,
				Description: "总额度最大值",
			},
			"availableAmountMin": {
				Type:        "number",
				Min:         &minVal,
				Max:         &maxVal,
				Description: "可用额度最小值",
			},
			"availableAmountMax": {
				Type:        "number",
				Min:         &minVal,
				Max:         &maxVal,
				Description: "可用额度最大值",
			},

			// 排序和时间信息
			"sortType": {
				Type:        "string",
				Enum:        []string{"last_repay_time_desc", "last_repay_time_asc", "available_amount_desc", "available_amount_asc"},
				Description: "排序类型：最后还款时间和可用额度排序",
			},
			"lastRepayTimeStart": {
				Type:        "string",
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}:\\d{2})?$",
				Description: "最后还款开始时间",
			},
			"lastRepayTimeEnd": {
				Type:        "string",
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}:\\d{2})?$",
				Description: "最后还款结束时间",
			},
			"billDueTimeStart": {
				Type:        "string",
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}:\\d{2})?$",
				Description: "账单到期开始时间",
			},
			"billDueTimeEnd": {
				Type:        "string",
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}( \\d{2}:\\d{2}:\\d{2})?$",
				Description: "账单到期结束时间",
			},

			// 分页参数
			"page": {
				Type:        "number",
				Default:     1,
				Description: "页码",
			},
			"pageSize": {
				Type:        "number",
				Default:     20,
				Description: "每页数量",
			},
		},
		Required: []string{}, // 所有参数都是可选的
	}
}

// GetSendRepurchaseSMSSchema 发送复购短信参数验证规则
func GetSendRepurchaseSMSSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "发送复购短信参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"customerId": {
				Type:        "number",
				Min:         floatPtr(1),
				Description: "客户ID",
			},
			"smsTemplate": {
				Type:        "string",
				MaxLength:   500,
				Description: "短信模板内容",
			},
		},
		Required: []string{"customerId"},
	}
}

// GetRecordRepurchaseAwakenSchema 记录复购唤醒参数验证规则
func GetRecordRepurchaseAwakenSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "记录复购唤醒参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"customerId": {
				Type:        "number",
				Min:         floatPtr(1),
				Description: "客户ID",
			},
			"awakenContent": {
				Type:        "string",
				MinLength:   1,
				MaxLength:   1000,
				Description: "唤醒内容（联系结果、短信内容等）",
			},
			"awakenType": {
				Type:        "integer",
				Min:         floatPtr(1),
				Max:         floatPtr(3),
				Default:     2,
				Description: "唤醒类型：1-短信唤醒，2-电话唤醒，3-人工记录",
			},
			"remark": {
				Type:        "string",
				MaxLength:   1000,
				Description: "备注小记",
			},
		},
		Required: []string{"customerId", "awakenContent"},
	}
}
