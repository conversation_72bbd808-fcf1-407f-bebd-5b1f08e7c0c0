package bankcard

import (
	"context"
	"net/http"
	"os"
	"reflect"
	"strconv"
	"strings"
	"time"

	"fincore/route/middleware"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
)

// BankCardController 银行卡控制器
type BankCardController struct{}

// 初始化银行卡服务
var bankCardService *BankCardService

func init() {
	// 注册路由到自动路由系统
	gf.Register(&BankCardController{}, reflect.TypeOf(BankCardController{}).PkgPath())
}

// 获取银行卡服务实例（延迟初始化）
func getBankCardService() *BankCardService {
	if bankCardService == nil && !isTestEnvironment() {
		bankCardService = NewBankCardService()
	}
	return bankCardService
}

// isTestEnvironment 检测是否为测试环境
func isTestEnvironment() bool {
	for _, arg := range os.Args {
		if strings.Contains(arg, "test") {
			return true
		}
	}
	return false
}

// PostBindInfo 绑定用户信息接口

func (bc *BankCardController) PostBindInfo(c *gin.Context) {
	// 1. 参数校验
	requestData := make(map[string]interface{})
	if err := c.ShouldBindJSON(&requestData); err != nil {
		results.Failed(c, "参数错误", err)
		return
	}

	schema := GetUserInfoSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 获取用户信息
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userID := uint(claim.ID)

	// 3. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	err := getBankCardService().BindUserInfo(ctx, userID, validationResult.Data)
	if err != nil {
		results.Failed(c, "绑定信息失败", err.Error())
		return
	}

	// 4. 返回结果
	results.Success(c, "绑定信息成功！", nil, nil)
}

// PostBindBankCardSms 发送银行卡绑定短信验证码接口

func (bc *BankCardController) PostBindBankCardSms(c *gin.Context) {
	// 1. 参数校验
	requestData := make(map[string]interface{})
	if err := c.ShouldBindJSON(&requestData); err != nil {
		results.Failed(c, "参数错误", err)
		return
	}

	schema := GetBankCardInfoSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 获取用户信息
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userID := uint(claim.ID)

	// 3. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := getBankCardService().SendBankCardSms(ctx, userID, validationResult.Data)
	if err != nil {
		results.Failed(c, err.Error(), nil)
		return
	}

	// 4. 返回结果
	results.Success(c, "短信验证码已发送，请输入验证码完成绑卡", result, nil)
}

// PostBindBankCard 绑定银行卡接口
func (bc *BankCardController) PostBindBankCard(c *gin.Context) {
	// 1. 参数校验
	requestData := make(map[string]interface{})
	if err := c.ShouldBindJSON(&requestData); err != nil {
		results.Failed(c, "参数错误", err)
		return
	}

	schema := GetBankCardBindSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(requestData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 获取用户信息
	claim := middleware.ParseToken(c.GetHeader("Authorization"))
	userID := uint(claim.ID)

	// 3. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	result, err := getBankCardService().BindBankCard(ctx, userID, validationResult.Data)
	if err != nil {
		results.Failed(c, err.Error(), nil)
		return
	}

	// 4. 返回结果
	results.Success(c, "银行卡绑定成功", result, nil)
}

// GetBankCardList 查询用户银行卡列表接口
// 路由: GET /business/bankcard/get_bank_card_list
func (bc *BankCardController) GetBankCardList(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := GetBankCardListSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 类型推断 - 将 customer_id 转换为 uint
	customerIDStr, ok := validationResult.Data["customer_id"].(string)
	if !ok {
		results.Failed(c, "customer_id 参数类型错误", nil)
		return
	}

	customerID, err := strconv.ParseUint(customerIDStr, 10, 32)
	if err != nil {
		results.Failed(c, "customer_id 必须是有效的数字", err.Error())
		return
	}

	// 3. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := getBankCardService().GetBankCardList(ctx, uint(customerID))
	if err != nil {
		results.Failed(c, "查询银行卡列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "查询成功", result, nil)
}

// GetHealth 健康检查接口
// 路由: GET /business/bankcard/get_health
func (bc *BankCardController) GetHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "fincore-bankcard-service",
		"version": "1.0.0",
		"time":    time.Now().Format("2006-01-02 15:04:05"),
	})
}
