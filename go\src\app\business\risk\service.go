package risk

import (
	"context"
	"encoding/json"
	"fmt"
	"math"
	"strconv"
	"time"

	"fincore/app/business/blacklist"
	"fincore/app/business/channel"
	"fincore/global"
	"fincore/model"
	"fincore/thirdparty/riskmodelservice"
	"fincore/thirdparty/riskthirdparty"
	"fincore/utils/lock"
	"fincore/utils/utilstool/goredis"

	"go.uber.org/zap"
)

// RiskEvaluationRequest 风控评估请求
type RiskEvaluationRequest struct {
	CustomerID uint64 `json:"customer_id" binding:"required"`
}

// RiskEvaluationResponse 风控评估响应
type RiskEvaluationResponse struct {
	RiskReportID         string  `json:"risk_report_id"`           // 风控报告ID
	RiskScore            int     `json:"risk_score"`               // 风控分数
	RiskResult           int     `json:"risk_result"`              // 风控结果
	AvailableCreditLimit float64 `json:"available_credit_limit"`   // 客户可用授信额度
	EvaluationTime       string  `json:"evaluation_time"`          // 评估时间
	FailureType          *string `json:"failure_type,omitempty"`   // 失败类型
	FailureReason        *string `json:"failure_reason,omitempty"` // 失败原因
}

// LoanProductsRequest 贷款产品匹配请求
type LoanProductsRequest struct {
	CustomerID uint64 `form:"customer_id" binding:"required"`
	ChannelID  uint64 `form:"channel_id" binding:"required"`
}

// LoanProduct 贷款产品信息
type LoanProduct struct {
	ProductID          int     `json:"product_id"`
	ProductName        string  `json:"product_name"`
	LoanAmount         float64 `json:"loan_amount"`
	LoanPeriod         int     `json:"loan_period"`
	AnnualInterestRate float64 `json:"annual_interest_rate"`
	InterestRate       float64 `json:"interest_rate"`
	RepaymentMethod    string  `json:"repayment_method"`
	ChannelName        string  `json:"channel_name"`
	ChannelID          uint64  `json:"channel_id"`
	MaxAmount          float64 `json:"max_amount"`
	MinAmount          float64 `json:"min_amount"`
	Term               int     `json:"term"`
	MinRiskScore       float64 `json:"min_value"`
	MaxRiskScore       float64 `json:"max_value"`
}

// LoanProductsResponse 贷款产品匹配响应
type LoanProductsResponse struct {
	OverallCreditLimit   float64               `json:"overall_credit_limit"`   // 客户授信额度
	AvailableCreditLimit float64               `json:"available_credit_limit"` // 客户可用授信额度
	Products             []*model.ProductRules `json:"products"`
}

// RiskReportResponse 风控报告响应
type RiskReportResponse struct {
	EvaluationID   string                 `json:"evaluation_id"`
	RiskScore      int                    `json:"risk_score"`
	RiskResult     int                    `json:"risk_result"`
	EvaluationTime string                 `json:"evaluation_time"`
	RawData        map[string]interface{} `json:"raw_data"`
}

// RiskService 风控服务
type RiskService struct {
	riskEvalService   *model.RiskEvaluationService
	productService    *model.ProductRulesService
	channelService    *channel.ChannelService
	thirdPartyService *riskthirdparty.RiskThirdPartyService
	riskModelService  *riskmodelservice.RiskModelService
	blacklistService  *blacklist.BlacklistService
}

// NewRiskService 创建风控服务实例
func NewRiskService(
	riskEvalService *model.RiskEvaluationService,
	productService *model.ProductRulesService,
	channelService *channel.ChannelService,
	thirdPartyService *riskthirdparty.RiskThirdPartyService,
	riskModelService *riskmodelservice.RiskModelService,
) *RiskService {
	return &RiskService{
		riskEvalService:   riskEvalService,
		productService:    productService,
		channelService:    channelService,
		thirdPartyService: thirdPartyService,
		riskModelService:  riskModelService,
		blacklistService:  blacklist.NewBlacklistService(),
	}
}

// validateAndGetCustomerInfo 验证参数并获取客户信息
func (s *RiskService) validateAndGetCustomerInfo(ctx context.Context, data map[string]interface{}) (uint64, *model.BusinessAppAccount, error) {
	customerID, err := s.GetCustomerIDFromData(data)
	if err != nil {
		return 0, nil, err
	}

	customerInfo, err := s.GetCustomerInfo(ctx, int64(customerID))
	if err != nil {
		return 0, nil, fmt.Errorf("获取客户信息失败: %v", err)
	}

	// 验证必要字段
	if customerInfo.Name == "" || customerInfo.IDCard == "" || customerInfo.Mobile == "" {
		return 0, nil, fmt.Errorf("客户信息不完整: 姓名、身份证号或手机号为空")
	}

	return customerID, customerInfo, nil
}

// checkBlacklist 检查黑名单并返回拒绝响应（如果命中）
func (s *RiskService) checkBlacklist(ctx context.Context, customerID uint64) *RiskEvaluationResponse {
	blacklistResult, err := s.blacklistService.CheckUserBlacklist(ctx, customerID)
	if err != nil {
		global.App.Log.Error("黑名单检查失败", zap.Error(err))
		return nil // 黑名单检查失败，继续后续流程
	}

	if !blacklistResult.IsBlacklisted {
		return nil // 未命中黑名单
	}

	// 命中黑名单，记录日志并返回拒绝结果
	global.App.Log.Info("用户命中内部黑名单",
		zap.Uint64("customer_id", customerID),
		zap.String("blacklist_type", string(blacklistResult.BlacklistType)),
		zap.Any("reasons", blacklistResult.Reasons))

	evaluationID := model.GenerateEvaluationID(int(customerID))
	failureType := riskmodelservice.FailureTypeInternalBlacklist
	failureReason := fmt.Sprintf("%v", blacklistResult.Details)

	// 存储黑名单拒绝的评估结果
	evaluation := &model.RiskEvaluation{
		EvaluationID:   evaluationID,
		CustomerID:     int(customerID),
		RiskScore:      0,
		RiskResult:     model.REJECTED,
		EvaluationTime: time.Now(),
		FailureType:    &failureType,
		FailureReason:  &failureReason,
	}

	if err := s.riskEvalService.CreateEvaluation(evaluation); err != nil {
		global.App.Log.Error("存储黑名单拒绝结果失败", zap.Error(err))
	}

	return &RiskEvaluationResponse{
		RiskReportID:   evaluationID,
		RiskScore:      0.0,
		RiskResult:     model.REJECTED,
		EvaluationTime: evaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
		FailureType:    &failureType,
		FailureReason:  &failureReason,
	}
}

// evaluateRiskModel 调用风控模型并处理结果
func (s *RiskService) evaluateRiskModel(ctx context.Context, customerID uint64, customerInfo *model.BusinessAppAccount) (*RiskEvaluationResponse, error) {
	evaluationID := model.GenerateEvaluationID(int(customerID))
	// 构建风控请求参数
	riskReq := &riskthirdparty.RiskRequest{
		Name:   customerInfo.Name,
		IDNo:   customerInfo.IDCard,
		Mobile: customerInfo.Mobile,
	}

	fmt.Printf("获取到的客户信息: %s %s %s\n", customerInfo.Name, customerInfo.IDCard, customerInfo.Mobile)

	// 调用风控模型服务
	modelResult, err := s.riskModelService.ProcessRiskEvaluationWithCustomer(ctx, int(customerID), riskReq)
	if err != nil {
		return nil, fmt.Errorf("风控模型评估失败: %v", err)
	}

	// 存储第三方风控原始数据（非外部黑名单情况）
	if modelResult.FailureType != riskmodelservice.FailureTypeExternalBlacklist {
		if err := s.storeThirdPartyRawData(evaluationID, modelResult); err != nil {
			fmt.Printf("存储第三方原始数据失败: %v\n", err)
		}
	}

	// 存储评估结果
	evaluation := s.buildEvaluation(evaluationID, customerID, modelResult)
	if err := s.riskEvalService.CreateEvaluation(evaluation); err != nil {
		return nil, fmt.Errorf("存储评估结果失败: %v", err)
	}
	// 检查渠道ID，如果是渠道1 不管风控结果怎么样 都改成通过 而且风控分数改成 1000
	if customerInfo.ChannelID == 1 {
		evaluation.RiskResult = model.APPROVED
		evaluation.RiskScore = model.MaxRiskScore
	}

	response, err := s.getProductsAndMaxCreditByScore(ctx, uint64(customerInfo.ChannelID), float64(evaluation.RiskScore))
	if err != nil {
		return nil, err
	}
	// 从business_app_account
	businessAppAccountService := model.NewBusinessAppAccountService()

	// 更新 business_app_account 中的 allQuota
	err = businessAppAccountService.UpdateAllQuota(int64(customerID), response.OverallCreditLimit)
	if err != nil {
		global.App.Log.Error("更新 allQuota 失败", zap.Error(err), zap.Uint64("customer_id", customerID), zap.Float64("new_quota", response.OverallCreditLimit))
		return nil, err
	}

	// 计算用户在借额度（status为0或1的订单loan_amount字段之和）
	borrowingAmount, err := s.getUserBorrowingAmount(ctx, int64(customerID))
	if err != nil {
		global.App.Log.Error("获取用户在借额度失败", zap.Error(err), zap.Uint64("customer_id", customerID))
		return nil, err
	}

	// 更新 business_app_account 中的 remainingQuota 根据用户在借的额度和当前给出的总额度计算当前可用额度，取值不小于0
	remainingQuota := response.OverallCreditLimit - borrowingAmount
	if remainingQuota < 0 {
		remainingQuota = 0
	}
	err = businessAppAccountService.UpdateReminderQuota(int64(customerID), remainingQuota)
	if err != nil {
		global.App.Log.Error("更新 reminderQuota 失败", zap.Error(err), zap.Uint64("customer_id", customerID), zap.Float64("new_quota", remainingQuota))
		return nil, err
	}

	response.AvailableCreditLimit = remainingQuota

	// 缓存风控结果
	s.cacheRiskResult(customerID, evaluationID, evaluation)

	return &RiskEvaluationResponse{
		RiskReportID:         evaluationID,
		RiskScore:            evaluation.RiskScore,
		RiskResult:           evaluation.RiskResult,
		AvailableCreditLimit: response.AvailableCreditLimit,
		EvaluationTime:       evaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
		FailureType:          evaluation.FailureType,
		FailureReason:        evaluation.FailureReason,
	}, nil
}

// processModelResult 处理模型结果
func (s *RiskService) processModelResult(modelResult *riskmodelservice.RiskEvaluationResult) *RiskModelResult {
	finalRiskResult := &RiskModelResult{
		RiskScore:   modelResult.FinalScore,
		RiskResult:  modelResult.FinalResult,
		CreditLimit: 0.0,
	}

	// 处理授信额度
	if modelResult.CreditLimit != nil && modelResult.CreditLimit.Code == 200 {
		if limit := modelResult.CreditLimit.CreditLimit; limit != "" {
			if parsedLimit, err := strconv.Atoi(limit); err != nil {
				global.App.Log.Error("模型授信额度格式有误", zap.Error(err))
			} else {
				finalRiskResult.CreditLimit = float64(parsedLimit)
			}
		}
	}

	return finalRiskResult
}

// buildEvaluation 构建评估结果对象
func (s *RiskService) buildEvaluation(evaluationID string, customerID uint64, modelResult *riskmodelservice.RiskEvaluationResult) *model.RiskEvaluation {
	evaluation := &model.RiskEvaluation{
		EvaluationID:   evaluationID,
		CustomerID:     int(customerID),
		RiskScore:      int(modelResult.FinalScore),
		RiskResult:     modelResult.FinalResult,
		EvaluationTime: time.Now(),
	}

	// 设置失败类型和原因
	if modelResult.FinalResult == model.REJECTED {
		if modelResult.FailureType != "" {
			evaluation.FailureType = &modelResult.FailureType
			evaluation.FailureReason = &modelResult.FailureReason
		} else if modelResult.FinalScore < 710 {
			failureType := riskmodelservice.FailureTypeRiskScore
			failureReason := "风控分数过低"
			evaluation.FailureType = &failureType
			evaluation.FailureReason = &failureReason
		}
	}

	return evaluation
}

// cacheRiskResult 缓存风控结果
func (s *RiskService) cacheRiskResult(customerID uint64, evaluationID string, evaluation *model.RiskEvaluation) {
	cacheData := map[string]interface{}{
		"risk_score":      evaluation.RiskScore,
		"risk_result":     evaluation.RiskResult,
		"evaluation_id":   evaluationID,
		"evaluation_time": evaluation.EvaluationTime,
	}

	// 添加失败类型和原因到缓存
	if evaluation.FailureType != nil {
		cacheData["failure_type"] = *evaluation.FailureType
	}
	if evaluation.FailureReason != nil {
		cacheData["failure_reason"] = *evaluation.FailureReason
	}

	if err := goredis.SetRiskScore(customerID, cacheData, 24*time.Hour); err != nil {
		fmt.Printf("缓存风控结果失败: %v\n", err)
	}
}

// getCustomerIDFromData 从数据中获取客户ID
func (s *RiskService) GetCustomerIDFromData(data map[string]interface{}) (uint64, error) {
	if customerIDVal, ok := data["customer_id"]; ok {
		switch v := customerIDVal.(type) {
		case string:
			if customerID, err := strconv.ParseUint(v, 10, 64); err == nil {
				return customerID, nil
			}
		case float64:
			return uint64(v), nil
		case int:
			return uint64(v), nil
		case int64:
			return uint64(v), nil
		case uint64:
			return v, nil
		}
	}
	return 0, fmt.Errorf("客户ID不能为空或格式错误")
}

// getProductIDFromData 从数据中获取产品ID
func (s *RiskService) getProductIDFromData(data map[string]interface{}) (int, error) {
	if productIDVal, ok := data["product_id"]; ok {
		switch v := productIDVal.(type) {
		case float64:
			return int(v), nil
		case int:
			return v, nil
		case int64:
			return int(v), nil
		case string:
			if id, err := strconv.Atoi(v); err == nil {
				return id, nil
			}
		}
	}
	return 0, fmt.Errorf("无效的产品ID")
}

// findProductByID 根据产品ID查找匹配的产品
func (s *RiskService) findProductByID(products []*model.ProductRules, productID int) *model.ProductRules {
	for _, product := range products {
		if product.ID == productID {
			return product
		}
	}
	return nil
}

// getChannelIDFromData 从数据中获取渠道ID
func (s *RiskService) GetChannelIDFromData(data map[string]interface{}) (uint64, error) {
	if channelIDVal, ok := data["channel_id"]; ok {
		switch v := channelIDVal.(type) {
		case string:
			if channelID, err := strconv.ParseUint(v, 10, 64); err == nil {
				return channelID, nil
			}
		case float64:
			return uint64(v), nil
		case int:
			return uint64(v), nil
		case uint64:
			return v, nil
		}
	}
	return 0, nil
}

// EvaluateRisk 执行基础风控评估（仅通过customerID）
func (s *RiskService) EvaluateRisk(ctx context.Context, data map[string]interface{}) (*RiskEvaluationResponse, error) {
	// 1. 参数验证和客户信息获取
	customerID, customerInfo, err := s.validateAndGetCustomerInfo(ctx, data)
	if err != nil {
		return nil, err
	}

	// 2. 黑名单检查
	if blacklistResponse := s.checkBlacklist(ctx, customerID); blacklistResponse != nil {
		return blacklistResponse, nil
	}

	// 3. 调用风控模型并处理结果
	result, err := s.evaluateRiskModel(ctx, customerID, customerInfo)

	return result, err

}

type EvaluateRiskWithProductParams struct {
	CustomerID uint64 `json:"customer_id" binding:"required"` // 客户ID
	ProductID  uint64 `json:"product_id" binding:"required"`  // 产品ID
	ChannelID  uint64 `json:"channel_id" binding:"required"`  // 渠道ID
}

// EvaluateRiskWithProduct 执行风控评估并匹配产品（通过customerID、productID和channelID）
func (s *RiskService) EvaluateRiskWithProduct(ctx context.Context, params EvaluateRiskWithProductParams) (*RiskEvaluationResponse, error) {
	// 1. 检查渠道ID，如果是渠道1，直接返回通过结果
	if params.ChannelID == 1 {
		// 渠道1特殊处理：直接通过，风控分数设为1000
		response := &RiskEvaluationResponse{
			RiskReportID:   fmt.Sprintf("channel1_%d_%d", params.CustomerID, time.Now().Unix()),
			RiskScore:      model.MaxRiskScore,
			RiskResult:     model.APPROVED,
			EvaluationTime: time.Now().Format("2006-01-02 15:04:05"),
		}

		// 通过风险分数获取匹配的产品和最大额度
		productsResponse, err := s.getProductsAndMaxCreditByScore(ctx, params.ChannelID, model.MaxRiskScore)
		if err != nil {
			return nil, fmt.Errorf("获取产品匹配失败: %v", err)
		}

		// 检查指定的产品是否在匹配列表中
		matchedProduct := s.findProductByID(productsResponse.Products, int(params.ProductID))
		if matchedProduct == nil {
			return nil, fmt.Errorf("指定的产品ID %d 不符合当前风险评估条件", params.ProductID)
		}

		response.AvailableCreditLimit = productsResponse.OverallCreditLimit

		return response, nil
	}

	// 2. 检查用户是否有待放款或放款中的订单
	hasLoanOrders, err := s.hasLoanOrders(ctx, int64(params.CustomerID))
	if err != nil {
		return nil, fmt.Errorf("检查用户订单状态失败: %v", err)
	}

	// 3. 检查是否有缓存的评估结果，如果距离上次评估时间小于1天则直接使用
	cacheData, err := goredis.GetRiskScore(params.CustomerID)
	if err == nil && cacheData != nil {
		// 检查缓存中的评估时间
		if evalTimeStr, ok := cacheData["evaluation_time"].(string); ok {
			if evalTime, parseErr := time.Parse("2006-01-02 15:04:05", evalTimeStr); parseErr == nil {
				timeDiff := time.Since(evalTime)
				if timeDiff < 24*time.Hour {
					// 距离上次评估时间小于1天，直接使用缓存结果
					riskScore := 0
					if score, ok := cacheData["risk_score"].(float64); ok {
						riskScore = int(score)
					} else if score, ok := cacheData["risk_score"].(int); ok {
						riskScore = score
					}

					riskResult := 0
					if result, ok := cacheData["risk_result"].(float64); ok {
						riskResult = int(result)
					}

					evaluationID := ""
					if id, ok := cacheData["evaluation_id"].(string); ok {
						evaluationID = id
					}

					// 如果风控结果被拒绝，直接返回
					if riskResult == model.REJECTED {
						return &RiskEvaluationResponse{
							RiskReportID:   evaluationID,
							RiskScore:      riskScore,
							RiskResult:     riskResult,
							EvaluationTime: evalTimeStr,
						}, nil
					}

					// 通过风险分数获取匹配的产品和最大额度
					productsResponse, err := s.getProductsAndMaxCreditByScore(ctx, params.ChannelID, float64(riskScore))
					if err != nil {
						return nil, fmt.Errorf("获取产品匹配失败: %v", err)
					}

					// 检查指定的产品是否在匹配列表中
					matchedProduct := s.findProductByID(productsResponse.Products, int(params.ProductID))
					if matchedProduct == nil {
						return nil, fmt.Errorf("指定的产品ID %d 不符合当前风险评估条件", params.ProductID)
					}

					// 从business_app_account表获取可用额度
					businessAppAccountService := model.NewBusinessAppAccountService()
					account, err := businessAppAccountService.GetBusinessAppAccountByID(int64(params.CustomerID))
					if err != nil {
						return nil, fmt.Errorf("获取账户信息失败: %v", err)
					}
					availableCreditLimit := math.Max(account.ReminderQuota, productsResponse.OverallCreditLimit)

					// 检查产品额度是否超过可用额度
					if availableCreditLimit < matchedProduct.LoanAmount {
						return nil, fmt.Errorf("可用额度不足：当前可用额度 %.2f，产品需要额度 %.2f", availableCreditLimit, matchedProduct.LoanAmount)
					}

					// 更新 business_app_account 中的 allQuota
					err = businessAppAccountService.UpdateAllQuota(int64(params.CustomerID), productsResponse.OverallCreditLimit)
					if err != nil {
						global.App.Log.Error("更新 allQuota 失败", zap.Error(err), zap.Uint64("customer_id", params.CustomerID), zap.Float64("new_quota", productsResponse.OverallCreditLimit))
					}

					return &RiskEvaluationResponse{
						RiskReportID:   evaluationID,
						RiskScore:      riskScore,
						RiskResult:     riskResult,
						EvaluationTime: evalTimeStr,
					}, nil
				}
			}
		}
	}

	// 4. 先获取上一次评估记录（如果存在）
	var lastEvaluation *model.RiskEvaluation
	if hasLoanOrders {
		// 有待放款或放款中订单时，先从缓存获取上一次评估
		if cacheData == nil {
			// 缓存未命中，从数据库查询最新评估记录
			lastEvaluation, err = s.riskEvalService.GetLatestEvaluationByCustomerID(int(params.CustomerID), int(params.ChannelID))
			if err != nil {
				return nil, fmt.Errorf("获取上一次评估记录失败: %v", err)
			}
		} else {
			// 从缓存构建评估记录
			lastEvaluation = &model.RiskEvaluation{
				RiskScore: func() int {
					if score, ok := cacheData["risk_score"].(float64); ok {
						return int(score)
					}
					if score, ok := cacheData["risk_score"].(int); ok {
						return score
					}
					return 0
				}(),
				RiskResult: func() int {
					if result, ok := cacheData["risk_result"].(float64); ok {
						return int(result)
					}
					return 0
				}(),
			}
		}
	}

	// 5. 执行新的风控评估
	baseEvaluation, err := s.EvaluateRisk(ctx, map[string]interface{}{
		"customer_id": params.CustomerID,
	})

	if err != nil {
		return nil, err
	}

	// 6. 如果风控结果被拒绝，直接返回
	if baseEvaluation.RiskResult == model.REJECTED {
		return baseEvaluation, nil
	}

	// 5. 根据是否有待放款或放款中订单处理风险分数
	finalRiskScore := baseEvaluation.RiskScore
	if hasLoanOrders && lastEvaluation != nil {
		// 有待放款或放款中订单：取上一次评估和本次评估的最小风险分数
		if lastEvaluation.RiskScore < baseEvaluation.RiskScore {
			finalRiskScore = lastEvaluation.RiskScore
		}
	}

	// 6. 通过风险分数获取匹配的产品和最大额度
	productsResponse, err := s.getProductsAndMaxCreditByScore(ctx, params.ChannelID, float64(finalRiskScore))
	if err != nil {
		return nil, fmt.Errorf("获取产品匹配失败: %v", err)
	}

	// 7. 检查指定的产品是否在匹配列表中
	matchedProduct := s.findProductByID(productsResponse.Products, int(params.ProductID))
	if matchedProduct == nil {
		return nil, fmt.Errorf("指定的产品ID %d 不符合当前风险评估条件", params.ProductID)
	}

	// 8. 从business_app_account表获取可用额度
	businessAppAccountService := model.NewBusinessAppAccountService()
	account, err := businessAppAccountService.GetBusinessAppAccountByID(int64(params.CustomerID))
	if err != nil {
		return nil, fmt.Errorf("获取账户信息失败: %v", err)
	}
	availableCreditLimit := math.Max(account.ReminderQuota, productsResponse.OverallCreditLimit)

	// 9. 检查产品额度是否超过可用额度
	if availableCreditLimit < matchedProduct.LoanAmount {
		return nil, fmt.Errorf("可用额度不足：当前可用额度 %.2f，产品需要额度 %.2f", availableCreditLimit, matchedProduct.LoanAmount)
	}
	// 判断一下产品中的 allQuota 是否不等于 account 中的 allQuote
	if productsResponse.OverallCreditLimit != account.AllQuota {
		// 10. 更新 business_app_account 中的 allQuota
		err = businessAppAccountService.UpdateAllQuota(int64(params.CustomerID), productsResponse.OverallCreditLimit)
		if err != nil {
			global.App.Log.Error("更新 allQuota 失败", zap.Error(err), zap.Uint64("customer_id", params.CustomerID), zap.Float64("new_quota", productsResponse.OverallCreditLimit))
		}
	}

	// 11. 更新数据库中的评估记录
	updateData := map[string]interface{}{
		"risk_score":      finalRiskScore,
		"risk_result":     baseEvaluation.RiskResult,
		"evaluation_time": time.Now(),
	}

	// 添加失败类型和原因（如果有）
	if baseEvaluation.FailureType != nil {
		updateData["failure_type"] = *baseEvaluation.FailureType
	}
	if baseEvaluation.FailureReason != nil {
		updateData["failure_reason"] = *baseEvaluation.FailureReason
	}

	// 12. 缓存风控结果
	cacheDataForSet := map[string]interface{}{
		"risk_score":      finalRiskScore,
		"risk_result":     baseEvaluation.RiskResult,
		"evaluation_id":   baseEvaluation.RiskReportID,
		"evaluation_time": updateData["evaluation_time"],
	}

	// 添加失败类型和原因到缓存
	if baseEvaluation.FailureType != nil {
		cacheDataForSet["failure_type"] = *baseEvaluation.FailureType
	}
	if baseEvaluation.FailureReason != nil {
		cacheDataForSet["failure_reason"] = *baseEvaluation.FailureReason
	}

	if err := goredis.SetRiskScore(params.CustomerID, cacheDataForSet, 24*time.Hour); err != nil {
		global.App.Log.Error("缓存风控结果失败", zap.Error(err), zap.Uint64("customer_id", params.CustomerID))
	}

	// 13. 更新返回结果中的风险分数
	baseEvaluation.RiskScore = finalRiskScore

	return baseEvaluation, nil
}

// GetLoanProducts 获取匹配的贷款产品
func (s *RiskService) GetLoanProducts(ctx context.Context, data map[string]interface{}) (*LoanProductsResponse, error) {
	// 1. 参数转换
	customerID, err := s.GetCustomerIDFromData(data)
	if err != nil {
		return nil, err
	}

	// 验证customerID不能为0
	if customerID == 0 {
		return nil, fmt.Errorf("客户ID不能为空")
	}

	channelID, err := s.GetChannelIDFromData(data)
	if err != nil {
		return nil, err
	}

	// 声明 cacheData 变量
	var cacheData map[string]interface{}

	// 获取用户锁，确保同一用户查询到结果唯一
	key := fmt.Sprintf("user_loan_lock_%d", customerID)

	loanLock := lock.GetLock(key)
	loanLock.Lock()
	global.App.Log.Info("获取用户贷款产品加锁成功", zap.Uint64("customer_id", customerID), zap.String("lock_key", key))
	defer loanLock.Unlock()

	// 2. 从缓存获取风控结果
	cacheData, err = goredis.GetRiskScore(customerID)

	if err != nil || cacheData == nil {
		// 缓存未命中，先从数据库读取最新评估记录
		latestEvaluation, dbErr := s.riskEvalService.GetLatestEvaluationByCustomerID(int(customerID), int(channelID))
		if dbErr == nil && latestEvaluation != nil {
			// 检查评估时间是否超过一天
			now := time.Now()
			timeDiff := now.Sub(latestEvaluation.EvaluationTime)
			if timeDiff < 24*time.Hour {
				// 评估时间未超过一天，使用数据库记录并重新设置缓存
				cacheData = map[string]interface{}{
					"risk_score":      latestEvaluation.RiskScore,
					"risk_result":     float64(latestEvaluation.RiskResult),
					"evaluation_id":   latestEvaluation.EvaluationID,
					"evaluation_time": latestEvaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
				}

				// 计算剩余过期时间并重新设置缓存
				remainingTime := 24*time.Hour - timeDiff
				if setErr := goredis.SetRiskScore(customerID, cacheData, remainingTime); setErr != nil {
					global.App.Log.Error("重新设置风控缓存失败", zap.Error(setErr), zap.Uint64("customer_id", customerID))
				}
			} else {
				// 评估时间超过一天，重新评估
				baseEvaluation, err := s.EvaluateRisk(ctx, map[string]interface{}{
					"customer_id": customerID,
				})
				if err != nil {
					return nil, err
				}
				cacheData = map[string]interface{}{
					"risk_score":      baseEvaluation.RiskScore,
					"risk_result":     baseEvaluation.RiskResult,
					"evaluation_id":   baseEvaluation.RiskReportID,
					"evaluation_time": baseEvaluation.EvaluationTime,
				}
			}
		} else {
			// 数据库中没有记录，重新评估
			baseEvaluation, err := s.EvaluateRisk(ctx, map[string]interface{}{
				"customer_id": customerID,
			})
			if err != nil {
				return nil, err
			}
			cacheData = map[string]interface{}{
				"risk_score":      baseEvaluation.RiskScore,
				"risk_result":     baseEvaluation.RiskResult,
				"evaluation_id":   baseEvaluation.RiskReportID,
				"evaluation_time": baseEvaluation.EvaluationTime,
			}
		}
	}

	// 3. 检查风控结果
	riskResult, _ := cacheData["risk_result"].(float64)
	if int(riskResult) == model.REJECTED {
		response := &LoanProductsResponse{
			OverallCreditLimit: 0,
			Products:           []*model.ProductRules{},
		}
		return response, nil
	}

	// 4. 获取风险分数
	riskScore := 0
	if score, ok := cacheData["risk_score"]; ok {
		if scoreFloat, ok := score.(float64); ok {
			riskScore = int(scoreFloat)
		} else if scoreInt, ok := score.(int); ok {
			riskScore = scoreInt
		}
	}

	// 5. 通过风险分数获取产品和最大额度
	response, err := s.getProductsAndMaxCreditByScore(ctx, channelID, float64(riskScore))
	if err != nil {
		return nil, err
	}

	// 6. 从business_app_account表获取可用额度
	businessAppAccountService := model.NewBusinessAppAccountService()
	account, err := businessAppAccountService.GetBusinessAppAccountByID(int64(customerID))
	if err != nil {
		return nil, fmt.Errorf("获取账户信息失败: %v", err)
	}
	response.AvailableCreditLimit = account.ReminderQuota

	// 8. 更新 business_app_account 中的 allQuota
	err = businessAppAccountService.UpdateAllQuota(int64(customerID), response.OverallCreditLimit)
	if err != nil {
		global.App.Log.Error("更新 allQuota 失败", zap.Error(err), zap.Uint64("customer_id", customerID), zap.Float64("new_quota", response.OverallCreditLimit))
	}

	// 9. 如果可用额度为0，直接返回空产品列表
	if response.AvailableCreditLimit == 0 {
		response.Products = []*model.ProductRules{}
		return response, nil
	}

	// 10. 过滤掉超出可用额度的产品（使用更高效的切片过滤）
	filteredProducts := make([]*model.ProductRules, 0, len(response.Products))
	for _, product := range response.Products {
		if product.LoanAmount <= response.AvailableCreditLimit {
			filteredProducts = append(filteredProducts, product)
		}
	}
	response.Products = filteredProducts
	return response, nil
}

// GetRiskReports 获取风控报告列表（支持时间查询）
func (s *RiskService) GetRiskReports(ctx context.Context, data map[string]interface{}) ([]RiskReportResponse, error) {
	// 1. 参数转换
	customerID, err := s.GetCustomerIDFromData(data)
	if err != nil {
		return nil, err
	}

	// 2. 获取时间参数
	startDate := ""
	endDate := ""
	if val, ok := data["start_date"]; ok {
		if str, ok := val.(string); ok {
			startDate = str
		}
	}
	if val, ok := data["end_date"]; ok {
		if str, ok := val.(string); ok {
			endDate = str
		}
	}

	// 3. 获取风控评估记录
	evaluations, err := s.riskEvalService.GetEvaluationsByCustomerIDAndDateRange(int(customerID), startDate, endDate)
	if err != nil {
		return nil, fmt.Errorf("未找到风控评估记录: %v", err)
	}

	if len(evaluations) == 0 {
		return []RiskReportResponse{}, nil
	}

	// 5. 构建响应数据
	var reports []RiskReportResponse
	for _, evaluation := range evaluations {
		// 获取原始数据
		rawData, err := s.riskEvalService.GetRawDataByEvaluationID(evaluation.EvaluationID)
		var rawDataMap map[string]interface{}
		rawDataMap = make(map[string]interface{})

		if err == nil {
			// 解析雷达V4数据
			if rawData.LeidaV4Response != "" {
				var leidaData map[string]interface{}
				if err := json.Unmarshal([]byte(rawData.LeidaV4Response), &leidaData); err == nil {
					rawDataMap["leida_v4"] = leidaData
				}
			}

			// 解析探真C数据
			if rawData.TanZhenCResponse != "" {
				var tanZhenData map[string]interface{}
				if err := json.Unmarshal([]byte(rawData.TanZhenCResponse), &tanZhenData); err == nil {
					rawDataMap["tan_zhen_c"] = tanZhenData
				}
			}
		}

		report := RiskReportResponse{
			EvaluationID:   evaluation.EvaluationID,
			RiskScore:      evaluation.RiskScore,
			RiskResult:     evaluation.RiskResult,
			EvaluationTime: evaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
			RawData:        rawDataMap,
		}
		reports = append(reports, report)
	}

	return reports, nil
}

// hasLoanOrders 检查用户是否有待放款或放款中的订单（status为0或1）
func (s *RiskService) hasLoanOrders(ctx context.Context, customerID int64) (bool, error) {
	// 查询用户是否有状态为0（待放款）或1（放款中）的订单
	count, err := model.DB().Table("business_loan_orders").
		Where("user_id", customerID).
		WhereIn("status", []interface{}{0, 1}). // 0-待放款，1-放款中
		Count()

	if err != nil {
		return false, fmt.Errorf("查询用户订单状态失败: %v", err)
	}

	return count > 0, nil
}

// getUserBorrowingAmount 计算用户在借额度（status为0或1的订单loan_amount字段之和）
func (s *RiskService) getUserBorrowingAmount(ctx context.Context, customerID int64) (float64, error) {
	// 查询用户状态为0（待放款）或1（放款中）的订单loan_amount字段之和
	result, err := model.DB().Table("business_loan_orders").
		Where("user_id", customerID).
		WhereIn("status", []interface{}{0, 1}). // 0-待放款，1-放款中
		Sum("loan_amount")

	if err != nil {
		return 0, fmt.Errorf("查询用户在借额度失败: %v", err)
	}

	// 将结果转换为float64
	if result == nil {
		return 0, nil
	}

	switch v := result.(type) {
	case float64:
		return v, nil
	case string:
		if amount, parseErr := strconv.ParseFloat(v, 64); parseErr == nil {
			return amount, nil
		}
		return 0, fmt.Errorf("无法解析在借额度: %v", v)
	default:
		return 0, fmt.Errorf("未知的在借额度类型: %T", v)
	}
}

// getCustomerInfo 从数据库获取客户信息
func (s *RiskService) GetCustomerInfo(ctx context.Context, customerID int64) (*model.BusinessAppAccount, error) {
	// 从数据库查询用户信息
	businessAppAccountService := model.NewBusinessAppAccountService()
	customer, err := businessAppAccountService.GetBusinessAppAccountByID(customerID)
	if err != nil {
		return nil, fmt.Errorf("查询账户失败: %v", err)
	}

	return customer, nil
}

// GetCustomerInfoCacheKey 生成客户信息缓存键
func GetCustomerInfoCacheKey(customerID uint64) string {
	return fmt.Sprintf("customer_info:%d", customerID)
}

// getMatchingProducts 获取匹配的贷款产品
func (s *RiskService) getMatchingProducts(ctx context.Context, channelID uint64, riskScore float64) ([]*model.ProductRules, error) {
	// 0. 先尝试从缓存获取产品规则
	cachedProducts, cacheErr := goredis.GetLoanProducts()
	if cacheErr == nil && cachedProducts != nil {
		// 缓存命中，将map数据转换为ProductRules结构体
		var productRules []*model.ProductRules
		for _, productMap := range cachedProducts {
			// 检查渠道匹配
			if channelIDFromCache, ok := productMap["channel_id"].(uint64); ok {
				if channelID != 0 && channelIDFromCache != channelID {
					continue
				}
			}
			// 安全的类型转换
			// 处理id的类型转换，支持int和float64
			var id int
			if val, ok := productMap["id"].(int); ok {
				id = val
			} else if val, ok := productMap["id"].(float64); ok {
				id = int(val)
			}
			ruleName, _ := productMap["rule_name"].(string)
			loanAmount, _ := productMap["loan_amount"].(float64)

			// 处理loan_period的类型转换，支持int和float64
			var loanPeriod int
			if val, ok := productMap["loan_period"].(int); ok {
				loanPeriod = val
			} else if val, ok := productMap["loan_period"].(float64); ok {
				loanPeriod = int(val)
			}

			// 处理total_periods的类型转换，支持int和float64
			var totalPeriods int
			if val, ok := productMap["total_periods"].(int); ok {
				totalPeriods = val
			} else if val, ok := productMap["total_periods"].(float64); ok {
				totalPeriods = int(val)
			}

			guaranteeFee, _ := productMap["guarantee_fee"].(float64)
			annualInterestRate, _ := productMap["annual_interest_rate"].(float64)
			otherFees, _ := productMap["other_fees"].(float64)
			ruleCategory, _ := productMap["rule_category"].(string)
			repaymentMethod, _ := productMap["repayment_method"].(string)
			createdAt, _ := productMap["created_at"].(float64)
			updatedAt, _ := productMap["updated_at"].(float64)

			productRule := &model.ProductRules{
				ID:                 id,
				RuleName:           ruleName,
				LoanAmount:         loanAmount,
				LoanPeriod:         loanPeriod,
				TotalPeriods:       totalPeriods,
				GuaranteeFee:       guaranteeFee,
				AnnualInterestRate: annualInterestRate,
				OtherFees:          otherFees,
				RuleCategory:       ruleCategory,
				RepaymentMethod:    repaymentMethod,
				CreatedAt:          int64(createdAt),
				UpdatedAt:          int64(updatedAt),
			}
			productRules = append(productRules, productRule)
		}

		if len(productRules) > 0 {
			global.App.Log.Info("从缓存获取产品规则成功", zap.Int("count", len(productRules)))
			return productRules, nil
		}
	}

	global.App.Log.Info("缓存未命中，从数据库查询产品规则")

	// 1. 从数据库获取渠道信息
	channelService := model.NewChannelService()
	var channels []model.Channel
	var err error

	if channelID == 0 {
		// 获取所有活跃渠道
		channels, err = channelService.GetActiveChannels()
	} else {
		// 获取指定渠道
		channel, err := channelService.GetChannelByID(int(channelID))
		if err != nil {
			return nil, fmt.Errorf("查询渠道失败: %v", err)
		}
		if channel != nil && channel.ChannelStatus == 1 {
			channels = append(channels, *channel)
		}
	}
	global.App.Log.Info("len(channels)......", zap.Int("len", len(channels)))

	if err != nil {
		return nil, fmt.Errorf("查询渠道失败: %v", err)
	}

	if len(channels) == 0 {
		return []*model.ProductRules{}, nil
	}

	// 2. 获取匹配的规则ID
	var matchedRuleIDs []uint64
	for _, channel := range channels {
		// 解析loan_rules字段
		if channel.LoanRules == "" {
			continue
		}

		var loanRules []LoanRule
		if err := json.Unmarshal([]byte(channel.LoanRules), &loanRules); err != nil {
			fmt.Printf("解析渠道%d的loan_rules失败: %v\n", channel.ID, err)
			continue
		}

		// 根据风险分数匹配规则
		for _, rule := range loanRules {
			fmt.Printf("rule.MinRiskScore,rule.MaxRiskScore.......%f, %f\n", rule.MinRiskScore, rule.MaxRiskScore)
			// 向下兼容的匹配规则，如果风险分数小于等于规则的最大分数，就匹配
			if riskScore >= rule.MinRiskScore {
				matchedRuleIDs = append(matchedRuleIDs, rule.RuleID)
			}
		}
	}

	if len(matchedRuleIDs) == 0 {
		return []*model.ProductRules{}, nil
	}

	// 3. 根据规则ID获取产品
	productRulesService := &model.ProductRulesService{}
	productRulesSlice, err := productRulesService.GetProductRulesByIDs(matchedRuleIDs)
	if err != nil {
		return nil, fmt.Errorf("查询产品规则失败: %v", err)
	}

	// 4. 缓存产品规则到Redis
	productsForCache := make([]map[string]interface{}, 0, len(productRulesSlice))
	for _, rule := range productRulesSlice {
		productMap := map[string]interface{}{
			"id":                   rule.ID,
			"rule_name":            rule.RuleName,
			"loan_amount":          rule.LoanAmount,
			"loan_period":          rule.LoanPeriod,
			"total_periods":        rule.TotalPeriods,
			"guarantee_fee":        rule.GuaranteeFee,
			"annual_interest_rate": rule.AnnualInterestRate,
			"other_fees":           rule.OtherFees,
			"rule_category":        rule.RuleCategory,
			"repayment_method":     rule.RepaymentMethod,
			"created_at":           rule.CreatedAt,
			"updated_at":           rule.UpdatedAt,
			"channel_id":           channelID,
		}
		productsForCache = append(productsForCache, productMap)
	}

	// 调用Redis缓存
	if err := goredis.SetLoanProducts(productsForCache, 12*time.Hour); err != nil {
		global.App.Log.Warn("缓存产品规则失败", zap.Error(err))
	}

	return productRulesSlice, nil
}

// getProductsAndMaxCreditByScore 通过风险分数获取对应的产品和最大额度
func (s *RiskService) getProductsAndMaxCreditByScore(ctx context.Context, channelID uint64, riskScore float64) (*LoanProductsResponse, error) {
	// 1. 查询匹配的产品
	products, err := s.getMatchingProducts(ctx, channelID, riskScore)
	if err != nil {
		return nil, fmt.Errorf("查询产品失败: %v", err)
	}

	// 2. 计算总授信额度 - 取products中最大的LoanAmount
	var maxLoanAmount float64
	for _, product := range products {
		if product.LoanAmount > maxLoanAmount {
			maxLoanAmount = product.LoanAmount
		}
	}

	// 3. 构建响应
	response := &LoanProductsResponse{
		OverallCreditLimit: maxLoanAmount,
		Products:           products,
	}

	return response, nil
}

// GetRiskScoreCacheKey 获取风控分数缓存键
func GetRiskScoreCacheKey(customerID uint64) string {
	return fmt.Sprintf("risk:score:%d", customerID)
}

// 常量定义
const (
	RiskScoreCacheExpiration    = 24 * time.Hour
	CustomerInfoCacheExpiration = 1 * time.Hour
	LoanProductsCacheExpiration = 1 * time.Hour
)

// 模拟的第三方服务结构体
type RiskModelResult struct {
	RiskScore   float64 `json:"risk_score"`
	RiskResult  int     `json:"risk_result"`
	CreditLimit float64 `json:"credit_limit"`
}

// ChannelInfo 渠道信息结构体
type ChannelInfo struct {
	ID           int     `json:"id"`
	ChannelName  string  `json:"channel_name"`
	ChannelID    uint64  `json:"channel_id"`
	InterestRate float64 `json:"interest_rate"`
	MaxAmount    float64 `json:"max_amount"`
	MinAmount    float64 `json:"min_amount"`
	Term         int     `json:"term"`
	MinRiskScore float64 `json:"min_value"`
	MaxRiskScore float64 `json:"max_value"`
}

// LoanRule 放款规则结构
type LoanRule struct {
	RuleID       uint64  `json:"rule_id"`
	MinRiskScore float64 `json:"min_value"`
	MaxRiskScore float64 `json:"max_value"`
}

// GetCachedRiskEvaluation 从缓存获取风控评估结果
func (s *RiskService) GetCachedRiskEvaluation(ctx context.Context, data map[string]interface{}) (*RiskEvaluationResponse, error) {
	// 1. 参数转换
	customerID, err := s.GetCustomerIDFromData(data)
	if err != nil {
		return nil, err
	}

	// 验证customerID不能为0
	if customerID == 0 {
		return nil, fmt.Errorf("客户ID不能为空")
	}

	// 2. 从缓存获取风控结果
	cacheData, err := goredis.GetRiskScore(customerID)
	if err != nil || cacheData == nil {
		return nil, fmt.Errorf("缓存未命中")
	}

	// 3. 构建响应
	riskScore := 0.0
	if score, ok := cacheData["risk_score"]; ok {
		if scoreFloat, ok := score.(float64); ok {
			riskScore = scoreFloat
		} else {
			return nil, fmt.Errorf("缓存数据格式错误")
		}
	}

	riskResult, _ := cacheData["risk_result"].(float64)

	// 从缓存获取evaluation_id
	evaluationID, _ := cacheData["evaluation_id"].(string)

	// 从缓存获取evaluation_time并格式化
	evaluationTimeStr := ""
	if timeVal, ok := cacheData["evaluation_time"]; ok {
		if timeObj, ok := timeVal.(time.Time); ok {
			evaluationTimeStr = timeObj.Format("2006-01-02 15:04:05")
		} else if timeStr, ok := timeVal.(string); ok {
			// 如果已经是字符串格式，直接使用
			evaluationTimeStr = timeStr
		}
	}

	// 从缓存获取失败类型和原因
	var failureType *string
	var failureReason *string
	if ft, ok := cacheData["failure_type"]; ok {
		if ftStr, ok := ft.(string); ok {
			failureType = &ftStr
		}
	}
	if fr, ok := cacheData["failure_reason"]; ok {
		if frStr, ok := fr.(string); ok {
			failureReason = &frStr
		}
	}

	response := &RiskEvaluationResponse{
		RiskReportID:   evaluationID,
		RiskScore:      int(riskScore),
		RiskResult:     int(riskResult),
		EvaluationTime: evaluationTimeStr,
		FailureType:    failureType,
		FailureReason:  failureReason,
	}

	return response, nil
}

// storeThirdPartyRawData 存储第三方风控原始数据
func (s *RiskService) storeThirdPartyRawData(evaluationID string, modelResult *riskmodelservice.RiskEvaluationResult) error {
	rawData := &model.RiskRawData{
		EvaluationID: evaluationID,
		DataSource:   "xiamengbangzu", // 合并数据源
	}

	// 存储tan_zhen_c原始数据
	if tanZhenData, exists := modelResult.ThirdPartyResults["tanZhen"]; exists {
		tanZhenJSON, _ := json.Marshal(tanZhenData)
		rawData.TanZhenCResponse = string(tanZhenJSON)
	}

	// 存储leida_v4原始数据
	if leidaData, exists := modelResult.ThirdPartyResults["leida"]; exists {
		leidaJSON, _ := json.Marshal(leidaData)
		rawData.LeidaV4Response = string(leidaJSON)
	}

	// 只有当至少有一个数据源有数据时才存储
	if rawData.TanZhenCResponse != "" || rawData.LeidaV4Response != "" {
		err := s.riskEvalService.CreateRawData(rawData)
		if err != nil {
			return fmt.Errorf("存储第三方原始数据失败: %v", err)
		}
	}

	return nil
}
