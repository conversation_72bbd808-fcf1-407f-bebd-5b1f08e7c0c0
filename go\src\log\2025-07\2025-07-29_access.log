{"level":"dev.error","ts":"[2025-07-29 16:06:23.074]","caller":"log/middleware.go:164","msg":"HTTP请求","request_id":"1856aa54184db2d4584185c2","method":"GET","url":"/uniapp/order/get_order_bills","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":500,"response_time":0.0036964,"request_size":0,"response_size":96,"stacktrace":"fincore/utils/log.AccessLogMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:164\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\nfincore/utils/log.RequestIDMiddleware.func1\n\tD:/work/code/fincore/go/src/utils/log/middleware.go:40\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.CustomRecoveryWithWriter.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.LoggerWithConfig.func1\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240\ngithub.com/gin-gonic/gin.(*Context).Next\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173\ngithub.com/gin-gonic/gin.(*Engine).handleHTTPRequest\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616\ngithub.com/gin-gonic/gin.(*Engine).ServeHTTP\n\tC:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572\nnet/http.serverHandler.ServeHTTP\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301\nnet/http.(*conn).serve\n\tC:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102"}
{"level":"dev.info","ts":"[2025-07-29 16:06:38.841]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1856aa57c31190fc5ac05551","method":"GET","url":"/business/order/manager/getOrderBillInfo","query":"order_no=ORD202401010001","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.022115,"request_size":0,"response_size":96}
{"level":"dev.info","ts":"[2025-07-29 16:07:36.403]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1856aa65268defec0d8f8f80","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0811145,"request_size":38,"response_size":19017}
{"level":"dev.info","ts":"[2025-07-29 16:10:30.218]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1856aa8d9db947d826cc8a0c","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.09681,"request_size":38,"response_size":19017}
{"level":"dev.info","ts":"[2025-07-29 16:14:23.933]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1856aac409593ddc84a54644","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0775156,"request_size":38,"response_size":19017}
{"level":"dev.info","ts":"[2025-07-29 16:18:57.690]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1856aafde5dd545814d97c29","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":25.322927,"request_size":38,"response_size":19017}
{"level":"dev.info","ts":"[2025-07-29 16:19:31.018]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1856ab0a52e759fc9908654c","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":5.2820884,"request_size":38,"response_size":19017}
{"level":"dev.info","ts":"[2025-07-29 17:07:49.677]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1856adae6d98d7d4d836c68f","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0941543,"request_size":38,"response_size":19020}
{"level":"dev.info","ts":"[2025-07-29 17:08:35.114]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1856adb904305794d7ed090f","method":"POST","url":"/business/order/manager/listOrders","query":"","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0566603,"request_size":38,"response_size":19020}
{"level":"dev.info","ts":"[2025-07-29 17:16:42.360]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1856ae2a76e994c8b8c8a409","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"order_no=LO20250729PDIMOSVG&page=1&pageSize=20","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.0445767,"request_size":0,"response_size":954}
{"level":"dev.info","ts":"[2025-07-29 17:17:56.234]","caller":"log/middleware.go:168","msg":"HTTP请求","request_id":"1856ae3ba9a83b4cbe664a12","method":"GET","url":"/business/order/manager/getOrderPaymentRecords","query":"order_no=LO20250729PDIMOSVG&page=1&pageSize=20","ip":"************","user_agent":"Apifox/1.0.0 (https://apifox.com)","status_code":200,"response_time":0.053707,"request_size":0,"response_size":980}
