package risk

import "fincore/utils/jsonschema"

// GetRiskEvaluationSchema 风控评估的参数验证规则
func GetRiskEvaluationSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "风控评估参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"customer_id": {
				Type:        "number",
				Required:    true,
				Min:         func() *float64 { v := 1.0; return &v }(),
				Description: "客户ID",
			},
			"channel_id": {
				Type:        "number",
				Required:    false,
				Min:         func() *float64 { v := 0.0; return &v }(),
				Description: "渠道来源",
			},
		},
		Required: []string{"customer_id"},
	}
}

// GetLoanProductsSchema 贷款产品匹配的参数验证规则
func GetLoanProductsSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "贷款产品匹配参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"customer_id": {
				Type:        "number",
				Required:    true,
				Min:         func() *float64 { v := 1.0; return &v }(),
				Description: "客户ID",
			},
			"channel_id": {
				Type:        "number",
				Required:    false,
				Min:         func() *float64 { v := 1.0; return &v }(),
				Description: "渠道ID",
			},
		},
		Required: []string{"customer_id"},
	}
}

// GetRiskReportSchema 风控报告查询的参数验证规则
func GetRiskReportSchema() jsonschema.Schema {
	return jsonschema.Schema{
		Title: "风控报告查询参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"customer_id": {
				Type:        "number",
				Required:    true,
				Min:         func() *float64 { v := 1.0; return &v }(),
				Description: "客户ID",
			},
			"start_date": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "开始日期，格式：YYYY-MM-DD",
			},
			"end_date": {
				Type:        "string",
				Required:    false,
				Pattern:     "^\\d{4}-\\d{2}-\\d{2}$",
				Description: "结束日期，格式：YYYY-MM-DD",
			},
		},
		Required: []string{"customer_id"},
	}
}
