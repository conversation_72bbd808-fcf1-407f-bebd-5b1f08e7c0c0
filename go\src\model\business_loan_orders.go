package model

import (
	"fincore/utils/gform"
	"fmt"
	"time"
)

// 订单状态常量定义
const (
	// 待放款 - 订单创建后等待风控或人工审核
	OrderStatusPendingDisbursement = 0
	// 放款中 - 已经成功放款，等待还款
	OrderStatusDisbursed = 1
	// 交易关闭 - 审核未通过的订单
	OrderStatusClosed = 2
	// 交易完成 - 放款成功且所有还款计划已完成
	OrderStatusCompleted = 3
	// 已退款 - 订单已退款
	OrderStatusRefunded = 4
)

// 订单状态描述映射
var OrderStatusDescriptions = map[int]string{
	OrderStatusPendingDisbursement: "待放款",
	OrderStatusDisbursed:           "放款中",
	OrderStatusClosed:              "交易关闭",
	OrderStatusCompleted:           "交易完成",
}

// BusinessLoanOrders 贷款订单数据模型
type BusinessLoanOrders struct {
	ID                    uint       `json:"id" gorm:"primaryKey;autoIncrement" db:"id"`                // 主键ID，自增
	OrderNo               string     `json:"order_no" db:"order_no"`                                    // 订单号，唯一标识
	UserID                uint       `json:"user_id" db:"user_id"`                                      // 用户ID，关联用户表
	ProductRuleID         uint       `json:"product_rule_id" db:"product_rule_id"`                      // 产品规则ID，关联产品规则表
	LoanAmount            Decimal    `json:"loan_amount,string" db:"loan_amount"`                       // 申请贷款金额，单位：元
	Principal             Decimal    `json:"principal,string" db:"principal"`                           // 实际放款本金，单位：元（前置付款时会扣除利息和费用）
	TotalInterest         Decimal    `json:"total_interest,string" db:"total_interest"`                 // 总利息，单位：元
	TotalGuaranteeFee     Decimal    `json:"total_guarantee_fee,string" db:"total_guarantee_fee"`       // 总担保费，单位：元
	TotalOtherFees        Decimal    `json:"total_other_fees,string" db:"total_other_fees"`             // 总其他费用，单位：元
	TotalRepayableAmount  Decimal    `json:"total_repayable_amount,string" db:"total_repayable_amount"` // 总应还金额，单位：元
	AmountPaid            Decimal    `json:"amount_paid,string" db:"amount_paid"`                       // 已还金额，单位：元
	CardID                uint       `json:"card_id" db:"card_id"`                                      // 银行卡ID，关联银行卡表
	ChannelID             int        `json:"channel_id" db:"channel_id"`                                // 渠道ID，关联渠道表
	RiskControlResult     int        `json:"risk_control_result" db:"risk_control_result"`              // 风控结果，0:通过，1:待复审，2:拒绝, 3:调用失败
	CustomerOrigin        string     `json:"customer_origin" db:"customer_origin"`                      // 客户来源
	InitialOrderChannelID int        `json:"initial_order_channel_id" db:"initial_order_channel_id"`    // 初始订单渠道ID，关联渠道表
	PaymentChannelID      *uint      `json:"payment_channel_id" db:"payment_channel_id"`                // 支付渠道ID，可为空
	Status                int8       `json:"status" db:"status"`                                        // 订单状态：0-待放款，1-放款中，2-交易关闭，3-交易完成
	IsFreeze              uint8      `json:"is_freeze" db:"is_freeze"`                                  // 是否冻结：0-否，1-是
	IsRefundNeeded        uint8      `json:"is_refund_needed" db:"is_refund_needed"`                    // 是否需要退款：0-否，1-是
	ReasonForClosure      *uint      `json:"reason_for_closure" db:"reason_for_closure"`                // 关闭原因，可为空 订单关闭原因: 0-终审拒绝；1-法院涉案；2-纯白户；3-客户失联；4-不提供资料；5-多余订单；6-重新下单；7-客户不同意方案
	ClosureRemarks        *string    `json:"closure_remarks" db:"closure_remarks"`                      // 关闭备注，可为空
	AuditAssigneeID       *uint      `json:"audit_assignee_id" db:"audit_assignee_id"`                  // 审核员ID，可为空
	ReviewStatus          uint8      `json:"review_status" db:"review_status"`                          // 复审状态：0-未复审，1-复审通过，2-复审拒绝
	ReviewRemark          *string    `json:"review_remark" db:"review_remark"`                          // 复审备注，直接存储复审时的备注内容
	RemarksID             *int64     `json:"remarks_id" db:"remarks_id"`                                // 关联普通备注表ID，指向business_order_remarks表的主键
	SalesAssigneeID       *uint      `json:"sales_assignee_id" db:"sales_assignee_id"`                  // 销售员ID，可为空
	CollectionAssigneeID  *uint      `json:"collection_assignee_id" db:"collection_assignee_id"`        // 催收员ID，可为空
	ContractID            int32      `json:"contract_id" db:"contract_id"`                              // 合同ID
	CreatedAt             *time.Time `json:"created_at" db:"created_at"`                                // 创建时间，timestamp类型
	DisbursedAt           *time.Time `json:"disbursed_at" db:"disbursed_at"`                            // 放款时间，timestamp类型，可为空
	CompletedAt           *time.Time `json:"completed_at" db:"completed_at"`                            // 完成时间，timestamp类型，可为空
	UpdatedAt             *time.Time `json:"updated_at" db:"updated_at"`                                // 更新时间，timestamp类型
}

// TableName 指定表名
func (BusinessLoanOrders) TableName() string {
	return "business_loan_orders"
}

// GetStatusDescription 获取状态描述
func (o *BusinessLoanOrders) GetStatusDescription() string {
	if desc, exists := OrderStatusDescriptions[int(o.Status)]; exists {
		return desc
	}
	return "未知状态"
}

// IsPendingDisbursement 是否为待放款状态
func (o *BusinessLoanOrders) IsPendingDisbursement() bool {
	return int(o.Status) == OrderStatusPendingDisbursement
}

// IsDisbursed 是否为放款中状态（已成功放款）
func (o *BusinessLoanOrders) IsDisbursed() bool {
	return int(o.Status) == OrderStatusDisbursed
}

// IsClosed 是否为交易关闭状态
func (o *BusinessLoanOrders) IsClosed() bool {
	return int(o.Status) == OrderStatusClosed
}

// IsCompleted 是否为交易完成状态
func (o *BusinessLoanOrders) IsCompleted() bool {
	return int(o.Status) == OrderStatusCompleted
}

// CanStartDisbursement 是否可以开始放款
func (o *BusinessLoanOrders) CanStartDisbursement() bool {
	return int(o.Status) == OrderStatusPendingDisbursement
}

// CanMarkAsCompleted 是否可以标记为交易完成
func (o *BusinessLoanOrders) CanMarkAsCompleted() bool {
	return int(o.Status) == OrderStatusDisbursed
}

// CanClose 是否可以关闭订单
func (o *BusinessLoanOrders) CanClose() bool {
	return int(o.Status) == OrderStatusPendingDisbursement
}

// BusinessLoanOrdersService 贷款订单服务
type BusinessLoanOrdersService struct{}

// NewBusinessLoanOrdersService 创建贷款订单服务实例
func NewBusinessLoanOrdersService() *BusinessLoanOrdersService {
	return &BusinessLoanOrdersService{}
}

// GetClosureReasonText 获取关单原因文本描述
func (s *BusinessLoanOrdersService) GetClosureReasonText(reasonCode int) string {
	reasonTexts := map[int]string{
		0: "终审拒绝",
		1: "法院涉案",
		2: "纯白户",
		3: "客户失联",
		4: "不提供资料",
		5: "多余订单",
		6: "重新下单",
		7: "客户不同意方案",
	}

	if text, exists := reasonTexts[reasonCode]; exists {
		return text
	}
	return fmt.Sprintf("未知原因(%d)", reasonCode)
}

// GetOrderByID 根据ID获取订单
func (s *BusinessLoanOrdersService) GetOrderByID(id int) (*BusinessLoanOrders, error) {
	data, err := DB().Table("business_loan_orders").Where("id", id).First()
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if len(data) == 0 {
		return nil, fmt.Errorf("订单不存在")
	}

	var order BusinessLoanOrders
	if err := mapToStruct(data, &order); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &order, nil
}

// GetOrderByOrderNo 根据订单号获取订单
func (s *BusinessLoanOrdersService) GetOrderByOrderNo(orderNo string) (*BusinessLoanOrders, error) {
	data, err := DB().Table("business_loan_orders").Where("order_no", orderNo).First()
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if len(data) == 0 {
		return nil, nil
	}

	var order BusinessLoanOrders
	if err := mapToStruct(data, &order); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &order, nil
}

// GetPendingOrdersByUserID 根据用户ID获取待处理订单列表
func (s *BusinessLoanOrdersService) GetPendingOrdersByUserID(userID int) ([]BusinessLoanOrders, error) {
	if userID <= 0 {
		return nil, fmt.Errorf("用户ID无效")
	}

	// 查询状态为待放款(0)、放款中(1)的订单
	data, err := DB().Table("business_loan_orders").
		Where("user_id", userID).
		WhereIn("status", []interface{}{OrderStatusPendingDisbursement, OrderStatusDisbursed}).
		OrderBy("created_at DESC").
		Get()

	if err != nil {
		return nil, fmt.Errorf("查询用户待处理订单失败: %v", err)
	}

	var orders []BusinessLoanOrders
	for _, item := range data {
		var order BusinessLoanOrders
		if err := mapToStruct(item, &order); err != nil {
			return nil, fmt.Errorf("数据映射失败: %v", err)
		}
		orders = append(orders, order)
	}

	return orders, nil
}

// CountPendingDisbursementOrdersByUserID 统计用户待放款订单数量
func (s *BusinessLoanOrdersService) CountPendingDisbursementOrdersByUserID(userID int) (int64, error) {
	if userID <= 0 {
		return 0, fmt.Errorf("用户ID无效")
	}

	count, err := DB().Table("business_loan_orders").
		Where("user_id", userID).
		Where("status", OrderStatusPendingDisbursement).
		Count()

	if err != nil {
		return 0, fmt.Errorf("查询用户待放款订单数量失败: %v", err)
	}

	return count, nil
}

// CountInProgressOrdersByUserID 统计用户在途订单数量（待放款+放款中）
func (s *BusinessLoanOrdersService) CountInProgressOrdersByUserID(userID int) (int64, error) {
	if userID <= 0 {
		return 0, fmt.Errorf("用户ID无效")
	}

	count, err := DB().Table("business_loan_orders").
		Where("user_id", userID).
		WhereIn("status", []interface{}{OrderStatusPendingDisbursement, OrderStatusDisbursed}).
		Count()

	if err != nil {
		return 0, fmt.Errorf("查询用户在途订单数量失败: %v", err)
	}

	return count, nil
}

// DeleteOrder 删除订单
func (s *BusinessLoanOrdersService) DeleteOrder(id int) error {
	_, err := DB().Table("business_loan_orders").Where("id", id).Delete()
	if err != nil {
		return fmt.Errorf("删除订单失败: %v", err)
	}
	return nil
}

// UpdateOrderStatus 更新订单状态
func (s *BusinessLoanOrdersService) UpdateOrderStatus(tx gform.IOrm, id int, status int) error {
	if id <= 0 {
		return fmt.Errorf("订单ID无效")
	}

	// 验证状态值的有效性
	if _, exists := OrderStatusDescriptions[status]; !exists {
		return fmt.Errorf("无效的订单状态: %d", status)
	}

	data := map[string]interface{}{
		"status":     status,
		"updated_at": time.Now(),
	}

	// 根据状态设置相应的时间戳
	switch status {
	case OrderStatusDisbursed:
		// 放款成功时设置disbursed_at
		data["disbursed_at"] = time.Now()
	case OrderStatusCompleted:
		// 交易完成时设置completed_at
		data["completed_at"] = time.Now()
	case OrderStatusClosed:
		data["completed_at"] = time.Now()
	}

	var handel gform.IOrm

	if tx != nil {
		handel = tx.Table("business_loan_orders")
	} else {
		handel = DB().Table("business_loan_orders")
	}

	_, err := handel.Where("id", id).Data(data).Update()
	if err != nil {
		return fmt.Errorf("更新订单状态失败: %v", err)
	}

	return nil
}

// CompleteDisbursement 完成放款（状态从待放款变为放款中）
func (s *BusinessLoanOrdersService) CompleteDisbursement(id int) error {
	// 先查询当前订单状态
	order, err := s.GetOrderByID(id)
	if err != nil {
		return fmt.Errorf("查询订单失败: %v", err)
	}

	if !order.CanStartDisbursement() {
		return fmt.Errorf("订单状态不允许放款，当前状态: %s", order.GetStatusDescription())
	}

	return s.UpdateOrderStatus(nil, id, OrderStatusDisbursed)
}

// MarkAsCompleted 标记订单为交易完成（所有还款计划已完成）
func (s *BusinessLoanOrdersService) MarkAsCompleted(id int) error {
	// 先查询当前订单状态
	order, err := s.GetOrderByID(id)
	if err != nil {
		return fmt.Errorf("查询订单失败: %v", err)
	}

	if !order.CanMarkAsCompleted() {
		return fmt.Errorf("订单状态不允许标记为完成，当前状态: %s", order.GetStatusDescription())
	}

	return s.UpdateOrderStatus(nil, id, OrderStatusCompleted)
}

type UpdateOrderParams struct {
	AmountPaid  Decimal    `json:"amount_paid"`  // 已还金额，单位：元
	Status      int        `json:"status"`       // 订单状态：0-待放款，1-放款中，2-交易关闭，3-交易完成
	CompletedAt *time.Time `json:"completed_at"` // 完成时间，timestamp类型，可为空
	UpdatedAt   *time.Time `json:"updated_at"`   // 更新时间，timestamp类型，可为空
}

type UpdateOrderCondition struct {
	ID      int    `json:"id"`
	OrderNo string `json:"order_no"`
}

// UpdateOrder 更新订单信息
func (s *BusinessLoanOrdersService) UpdateOrder(tx gform.IOrm, condition UpdateOrderCondition, updateMap map[string]interface{}) error {
	if condition.ID <= 0 && condition.OrderNo == "" {
		return fmt.Errorf("订单ID无效")
	}

	var handel gform.IOrm

	if tx != nil {
		handel = tx.Table("business_loan_orders")
	} else {
		handel = DB().Table("business_loan_orders")
	}

	if condition.ID > 0 {
		handel = handel.Where("id", condition.ID)
	}

	if condition.OrderNo != "" {
		handel = handel.Where("order_no", condition.OrderNo)
	}

	_, err := handel.Update(updateMap)
	if err != nil {
		return fmt.Errorf("更新订单信息失败: %v", err)
	}

	return nil
}

// GetOrderByContractID 根据合同id获取订单
func (s *BusinessLoanOrdersService) GetOrderByContractID(contractID int) (*BusinessLoanOrders, error) {
	data, err := DB().Table("business_loan_orders").Where("contract_id", contractID).First()
	if err != nil {
		return nil, fmt.Errorf("查询订单失败: %v", err)
	}
	if len(data) == 0 {
		return nil, nil
	}

	var order BusinessLoanOrders
	if err := mapToStruct(data, &order); err != nil {
		return nil, fmt.Errorf("数据映射失败: %v", err)
	}

	return &order, nil
}
