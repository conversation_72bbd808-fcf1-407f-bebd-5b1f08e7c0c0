package risk

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"path/filepath"
	"sync"
	"testing"
	"time"

	"fincore/app/business/channel"
	"fincore/global"
	"fincore/model"
	"fincore/thirdparty/riskmodelservice"
	"fincore/thirdparty/riskthirdparty"
	"fincore/utils/utilstool/goredis"

	"go.uber.org/zap"
)

// setupTestEnvironment 设置测试环境，切换到正确的工作目录
func setupTestEnvironment() (string, error) {
	path, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// 切换到src目录，确保能找到resource/config.yml
	srcPath := filepath.Join(path, "../../../")
	err = os.Chdir(srcPath)
	if err != nil {
		return "", err
	}

	return path, nil // 返回原始路径，用于defer恢复
}

// restoreWorkingDirectory 恢复原始工作目录
func restoreWorkingDirectory(originalPath string) {
	if originalPath != "" {
		os.Chdir(originalPath)
	}
}

// TestRiskSystemIntegration 测试风控系统的主要功能
func TestRiskSystemIntegration(t *testing.T) {
	t.Log("=== 金融信贷风控系统测试 ===")

	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)

	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 跳过数据库初始化，使用模拟数据
	model.MyInit("test") // 注释掉数据库初始化，避免配置依赖

	goredis.InitRedisClient()

	// 1. 初始化服务
	riskEvalService := model.NewRiskEvaluationService()
	productService := model.NewProductRulesService()
	channelService := channel.NewChannelService()
	thirdPartyService := riskthirdparty.NewRiskThirdPartyService()
	riskModelService := riskmodelservice.NewRiskModelService()

	// 创建风控服务
	riskService := NewRiskService(
		riskEvalService,
		productService,
		channelService,
		thirdPartyService,
		riskModelService,
	)

	ctx := context.Background()
	testCustomerID := uint64(26)

	// 2. 测试授信评估
	t.Run("授信评估测试", func(t *testing.T) {
		evalData := map[string]interface{}{
			"customer_id": testCustomerID,
		}

		evalResp, err := riskService.EvaluateRisk(ctx, evalData)
		if err != nil {
			t.Errorf("授信评估失败: %v", err)
			return
		}

		t.Logf("授信评估成功:")
		t.Logf("  风控报告ID: %s", evalResp.RiskReportID)
		t.Logf("  风险分数: %d", evalResp.RiskScore)
		t.Logf("  授信结果: %d", evalResp.RiskResult)
		t.Logf("  评估时间: %s", evalResp.EvaluationTime)

		// 验证返回结果的基本有效性
		if evalResp.RiskReportID == "" {
			t.Error("风控报告ID不能为空")
		}
		if evalResp.RiskScore < 0 || evalResp.RiskScore > 1000 {
			t.Errorf("风险分数异常: %d", evalResp.RiskScore)
		}

	})

	// 等待一秒，确保数据已保存
	time.Sleep(1 * time.Second)

	// 3. 测试贷款产品匹配
	t.Run("贷款产品匹配测试", func(t *testing.T) {
		productData := map[string]interface{}{
			"customer_id": testCustomerID,
			"channel_id":  uint64(0),
		}

		productResp, err := riskService.GetLoanProducts(ctx, productData)
		if err != nil {
			t.Errorf("获取贷款产品失败: %v", err)
			return
		}

		t.Logf("贷款产品匹配成功:")
		t.Logf("  总授信额度: %.2f", productResp.OverallCreditLimit)
		t.Logf("  匹配产品数量: %d", len(productResp.Products))
		for i, product := range productResp.Products {
			t.Logf("  产品%d: %s (产品代码: %d, 利率: %.2f%%, 额度: %.0f)",
				i+1, product.RuleName, product.ID, product.AnnualInterestRate, product.LoanAmount)
		}

		// 验证产品匹配结果
		if productResp.OverallCreditLimit < 0 {
			t.Errorf("总授信额度不能为负数: %.2f", productResp.OverallCreditLimit)
		}
		if len(productResp.Products) == 0 {
			t.Log("警告: 没有匹配到任何产品")
		}
	})

	// 4. 测试风控报告查询
	t.Run("风控报告查询测试", func(t *testing.T) {
		reportData := map[string]interface{}{
			"customer_id": testCustomerID,
		}
		reports, err := riskService.GetRiskReports(ctx, reportData)
		if err != nil {
			t.Errorf("获取风控报告失败: %v", err)
			return
		}

		if len(reports) == 0 {
			t.Error("应该至少有一条风控报告记录")
			return
		}

		reportResp := reports[0]
		t.Logf("风控报告查询成功:")
		t.Logf("  评估ID: %s", reportResp.EvaluationID)
		t.Logf("  风险分数: %d", reportResp.RiskScore)
		t.Logf("  授信结果: %d", reportResp.RiskResult)

		// 显示原始数据摘要
		if reportResp.RawData != nil {
			rawData, _ := json.Marshal(reportResp.RawData)
			t.Logf("  原始数据: %s", string(rawData))
		}

		if reportResp.EvaluationID == "" {
			t.Error("评估ID不能为空")
		}
	})

	// 5. 测试不同渠道的产品匹配
	t.Run("不同渠道产品匹配测试", func(t *testing.T) {
		channels := []uint64{1, 2, 3, 0}
		for _, channelID := range channels {
			reqData := map[string]interface{}{
				"customer_id": testCustomerID,
				"channel_id":  channelID,
			}

			resp, err := riskService.GetLoanProducts(ctx, reqData)
			if err != nil {
				t.Errorf("渠道 %d 产品匹配失败: %v", channelID, err)
				continue
			}

			channelName := fmt.Sprintf("渠道%d", channelID)
			if channelID == 0 {
				channelName = "全部渠道"
			}
			t.Logf("  %s: %d个产品, 总额度: %.2f",
				channelName, len(resp.Products), resp.OverallCreditLimit)

			// 验证每个渠道的结果
			if resp.OverallCreditLimit < 0 {
				t.Errorf("渠道 %s 总授信额度不能为负数: %.2f", channelName, resp.OverallCreditLimit)
			}
		}
	})

	t.Log("=== 测试完成 ===")
}

// TestRiskEvaluationOnly 单独测试授信评估功能
func TestRiskEvaluationOnly(t *testing.T) {
	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)

	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")

	// 初始化服务
	riskEvalService := model.NewRiskEvaluationService()
	productService := model.NewProductRulesService()
	channelService := channel.NewChannelService()
	thirdPartyService := riskthirdparty.NewRiskThirdPartyService()
	riskModelService := riskmodelservice.NewRiskModelService()

	riskService := NewRiskService(
		riskEvalService,
		productService,
		channelService,
		thirdPartyService,
		riskModelService,
	)

	ctx := context.Background()
	testCases := []struct {
		name       string
		customerID uint64
		expectErr  bool
	}{
		{"正常客户", 17, false},
		{"高风险客户", 18, false},
		{"空客户ID", 0, true},
		{"字符串客户ID", 0, true}, // 测试参数转换
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			reqData := map[string]interface{}{}

			// 根据测试用例设置不同的参数格式
			if tc.name == "字符串客户ID" {
				reqData["customer_id"] = "invalid"
			} else {
				reqData["customer_id"] = tc.customerID
			}

			resp, err := riskService.EvaluateRisk(ctx, reqData)
			if tc.expectErr {
				if err == nil {
					t.Error("期望出现错误，但没有错误")
				}
				t.Logf("预期错误: %v", err)
				return
			}

			if err != nil {
				t.Errorf("不期望出现错误: %v", err)
				return
			}

			if resp == nil {
				t.Error("响应不能为空")
				return
			}

			// 验证响应字段
			if resp.RiskReportID == "" {
				t.Error("风控报告ID不能为空")
			}
			if resp.RiskScore < 0 || resp.RiskScore > 1000 {
				t.Errorf("风险分数超出范围: %d", resp.RiskScore)
			}

			if resp.RiskResult < 0 || resp.RiskResult > 3 {
				t.Errorf("风险结果超出范围: %d", resp.RiskResult)
			}
			if resp.EvaluationTime == "" {
				t.Error("评估时间不能为空")
			}

		})
	}
}

// TestRiskReport 测试风控报告查询功能
func TestRiskReport(t *testing.T) {
	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)
	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")

	// 初始化服务
	riskEvalService := model.NewRiskEvaluationService()
	productService := model.NewProductRulesService()
	channelService := channel.NewChannelService()
	thirdPartyService := riskthirdparty.NewRiskThirdPartyService()
	riskModelService := riskmodelservice.NewRiskModelService()

	riskService := NewRiskService(
		riskEvalService,
		productService,
		channelService,
		thirdPartyService,
		riskModelService,
	)

	ctx := context.Background()

	// 先创建一个风控评估记录
	evalData := map[string]interface{}{
		"customer_id": uint64(17),
	}
	evalResp, err := riskService.EvaluateRisk(ctx, evalData)
	if err != nil {
		t.Logf("创建测试风控评估记录失败: %v", err)
	} else {
		t.Logf("创建评估记录成功: %s", evalResp.RiskReportID)
	}

	// 等待数据保存
	time.Sleep(100 * time.Millisecond)

	testCases := []struct {
		name        string
		customerID  uint64
		startDate   string
		endDate     string
		expectErr   bool
		description string
	}{
		{"正常查询", 17, "", "", false, "查询存在的客户风控报告"},
		{"不存在客户", 9999, "", "", false, "查询不存在的客户"},
		{"空客户ID", 0, "", "", true, "测试空客户ID错误处理"},
		{"字符串客户ID", 0, "", "", true, "测试无效客户ID格式"},
		{"时间范围查询", 17, "2025-01-01", "2025-12-31", false, "测试时间范围查询功能"},
		{"无效时间范围", 17, "2025-12-31", "2025-01-01", false, "测试无效时间范围"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Logf("测试用例: %s - %s", tc.name, tc.description)

			reqData := map[string]interface{}{}

			// 根据测试用例设置不同的参数格式
			if tc.name == "字符串客户ID" {
				reqData["customer_id"] = "invalid"
			} else {
				reqData["customer_id"] = tc.customerID
			}

			// 添加时间参数
			if tc.startDate != "" {
				reqData["start_date"] = tc.startDate
			}
			if tc.endDate != "" {
				reqData["end_date"] = tc.endDate
			}

			reports, err := riskService.GetRiskReports(ctx, reqData)
			if tc.expectErr {
				if err == nil {
					t.Error("期望出现错误，但没有错误")
				}
				t.Logf("预期错误: %v", err)
				return
			}

			if err != nil {
				t.Errorf("不期望出现错误: %v", err)
				return
			}

			if reports == nil {
				t.Error("响应不能为空")
				return
			}

			// 对于"不存在客户"的情况，期望返回空数组
			if tc.name == "不存在客户" {
				if len(reports) != 0 {
					t.Errorf("不存在客户应该返回空数组，但返回了 %d 条记录", len(reports))
				}
				return
			}

			// 对于"无效时间范围"的情况，期望返回空数组（开始时间晚于结束时间）
			if tc.name == "无效时间范围" {
				if len(reports) != 0 {
					t.Errorf("无效时间范围应该返回空数组，但返回了 %d 条记录", len(reports))
				}
				return
			}

			if len(reports) == 0 {
				t.Error("应该至少有一条风控报告记录")
				return
			}

			// 验证第一条记录的响应字段
			resp := reports[0]

			if resp.EvaluationID == "" {
				t.Error("评估ID不能为空")
			}
			if resp.RiskScore < 0 || resp.RiskScore > 1000 {
				t.Errorf("风险分数超出范围: %d", resp.RiskScore)
			}

			if resp.RiskResult < 0 || resp.RiskResult > 3 {
				t.Errorf("风险结果超出范围: %d", resp.RiskResult)
			}
			if resp.EvaluationTime == "" {
				t.Error("评估时间不能为空")
			}
			// 验证原始数据
			if resp.RawData != nil {
				t.Logf("原始数据字段数量: %d", len(resp.RawData))
			}

			t.Logf("报告查询结果: 共%d条记录, 第一条: ID=%s, Score=%d, Result=%d, Time=%s",
				len(reports), resp.EvaluationID, resp.RiskScore, resp.RiskResult, resp.EvaluationTime)
		})
	}
}

// TestRiskServiceIntegration 集成测试
func TestRiskServiceIntegration(t *testing.T) {
	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger
	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)
	// 初始化数据库连接
	model.MyInit("test")

	// 初始化服务
	riskEvalService := model.NewRiskEvaluationService()
	productService := model.NewProductRulesService()
	channelService := channel.NewChannelService()
	thirdPartyService := riskthirdparty.NewRiskThirdPartyService()
	riskModelService := riskmodelservice.NewRiskModelService()

	riskService := NewRiskService(
		riskEvalService,
		productService,
		channelService,
		thirdPartyService,
		riskModelService,
	)

	ctx := context.Background()
	testCustomerID := uint64(17)

	t.Run("完整流程测试", func(t *testing.T) {
		// 1. 执行风控评估
		t.Log("步骤1: 执行风控评估")
		evalData := map[string]interface{}{
			"customer_id": testCustomerID,
		}
		evalResp, err := riskService.EvaluateRisk(ctx, evalData)
		if err != nil {
			t.Fatalf("风控评估失败: %v", err)
		}
		t.Logf("评估完成: %s", evalResp.RiskReportID)

		// 2. 获取贷款产品
		t.Log("步骤2: 获取贷款产品")
		productData := map[string]interface{}{
			"customer_id": testCustomerID,
			"channel_id":  uint64(1),
		}
		productResp, err := riskService.GetLoanProducts(ctx, productData)
		if err != nil {
			t.Fatalf("获取产品失败: %v", err)
		}
		t.Logf("产品匹配完成: %d个产品", len(productResp.Products))

		// 3. 查询风控报告
		t.Log("步骤3: 查询风控报告")
		reportData := map[string]interface{}{
			"customer_id": testCustomerID,
		}
		reports, err := riskService.GetRiskReports(ctx, reportData)
		if err != nil {
			t.Fatalf("查询报告失败: %v", err)
		}
		if len(reports) == 0 {
			t.Fatal("应该至少有一条风控报告记录")
		}
		reportResp := reports[0]
		t.Logf("报告查询完成: %s", reportResp.EvaluationID)

		// 4. 验证数据一致性
		t.Log("步骤4: 验证数据一致性")
		if evalResp.RiskReportID != reportResp.EvaluationID {
			t.Errorf("评估ID不一致: %s != %s", evalResp.RiskReportID, reportResp.EvaluationID)
		}
		if evalResp.RiskScore != reportResp.RiskScore {
			t.Errorf("风险分数不一致: %d != %d", evalResp.RiskScore, reportResp.RiskScore)
		}
		t.Log("集成测试完成")
	})

	t.Run("并发测试", func(t *testing.T) {
		t.Log("执行并发测试")
		var wg sync.WaitGroup
		errorChan := make(chan error, 10)

		// 并发执行多个评估
		for i := 0; i < 5; i++ {
			wg.Add(1)
			go func(customerID uint64) {
				defer wg.Done()
				evalData := map[string]interface{}{
					"customer_id": customerID,
				}
				_, err := riskService.EvaluateRisk(ctx, evalData)
				if err != nil {
					errorChan <- fmt.Errorf("客户%d评估失败: %v", customerID, err)
				}
			}(uint64(20 + i))
		}

		wg.Wait()
		close(errorChan)

		// 检查错误
		for err := range errorChan {
			t.Errorf("并发测试错误: %v", err)
		}

		t.Log("并发测试完成")
	})
}

// TestProductMatching 单独测试产品匹配功能 - 使用真实channel数据
func TestProductMatching(t *testing.T) {
	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)

	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")
	goredis.InitRedisClient()

	// 初始化服务
	riskEvalService := model.NewRiskEvaluationService()
	productService := model.NewProductRulesService()
	channelService := channel.NewChannelService()
	thirdPartyService := riskthirdparty.NewRiskThirdPartyService()
	riskModelService := riskmodelservice.NewRiskModelService()

	riskService := NewRiskService(
		riskEvalService,
		productService,
		channelService,
		thirdPartyService,
		riskModelService,
	)

	ctx := context.Background()

	// 先创建一个风控评估记录用于测试
	evalData := map[string]interface{}{
		"customer_id": uint64(5),
	}
	riskEvaluationResponse, err := riskService.EvaluateRisk(ctx, evalData)
	if err != nil {
		t.Logf("创建测试风控评估记录失败: %v", err)
	}
	//通过 riskscore 去匹配
	products, err := riskService.getMatchingProducts(ctx, uint64(1), float64(riskEvaluationResponse.RiskScore))
	if err != nil {
		t.Logf("产品匹配失败: %v", err)
	}

	// 打印匹配结果
	for _, product := range products {
		t.Logf("产品名称: %s, 产品代码: %d, 产品金额: %.2f", product.RuleName, product.ID, product.LoanAmount)
	}
}

// TestEvaluateRiskWithProduct 测试风控评估并匹配产品功能
func TestEvaluateRiskWithProduct(t *testing.T) {
	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)

	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")
	goredis.InitRedisClient()

	// 初始化服务
	riskEvalService := model.NewRiskEvaluationService()
	productService := model.NewProductRulesService()
	channelService := channel.NewChannelService()
	thirdPartyService := riskthirdparty.NewRiskThirdPartyService()
	riskModelService := riskmodelservice.NewRiskModelService()

	riskService := NewRiskService(
		riskEvalService,
		productService,
		channelService,
		thirdPartyService,
		riskModelService,
	)

	ctx := context.Background()

	// 先获取可用的产品列表用于测试
	productData := map[string]interface{}{
		"customer_id": uint64(17),
		"channel_id":  uint64(0),
	}

	productResp, err := riskService.GetLoanProducts(ctx, productData)
	if err != nil {
		t.Fatalf("获取产品列表失败: %v", err)
	}

	if len(productResp.Products) == 0 {
		t.Skip("没有可用的产品进行测试")
	}

	// 选择第一个产品进行测试
	testProduct := productResp.Products[0]
	t.Logf("使用测试产品: %s (ID: %d, 金额: %.2f)", testProduct.RuleName, testProduct.ID, testProduct.LoanAmount)

	testCases := []struct {
		name        string
		customerID  uint64
		productID   uint64
		channelID   uint64
		expectErr   bool
		description string
	}{
		{
			name:        "正常流程-新用户",
			customerID:  uint64(100 + time.Now().Unix()%1000), // 使用动态客户ID避免冲突
			productID:   uint64(testProduct.ID),
			channelID:   uint64(0),
			expectErr:   true, // 新用户没有完整信息，期望失败
			description: "测试新用户的正常风控评估和产品匹配流程",
		},
		{
			name:        "正常流程-老用户",
			customerID:  uint64(17), // 使用已有评估记录的客户
			productID:   uint64(testProduct.ID),
			channelID:   uint64(0),
			expectErr:   false,
			description: "测试有历史记录用户的风控评估和产品匹配流程",
		},
		{
			name:        "无效客户ID",
			customerID:  uint64(0),
			productID:   uint64(testProduct.ID),
			channelID:   uint64(0),
			expectErr:   true,
			description: "测试无效客户ID的错误处理",
		},
		{
			name:        "无效产品ID",
			customerID:  uint64(17),
			productID:   uint64(99999), // 不存在的产品ID
			channelID:   uint64(0),
			expectErr:   true,
			description: "测试不存在产品ID的错误处理",
		},
		{
			name:        "指定渠道",
			customerID:  uint64(17),
			productID:   uint64(testProduct.ID),
			channelID:   uint64(1),
			expectErr:   false, // 由于重复evaluation_id问题，暂时期望失败
			description: "测试指定渠道的产品匹配",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Logf("测试用例: %s - %s", tc.name, tc.description)

			// 构建测试参数
			params := EvaluateRiskWithProductParams{
				CustomerID: tc.customerID,
				ProductID:  tc.productID,
				ChannelID:  tc.channelID,
			}

			// 执行测试
			resp, err := riskService.EvaluateRiskWithProduct(ctx, params)

			// 验证错误期望
			if tc.expectErr {
				if err == nil {
					t.Error("期望出现错误，但没有错误")
				}
				t.Logf("预期错误: %v", err)
				return
			}

			// 验证成功情况
			if err != nil {
				t.Errorf("不期望出现错误: %v", err)
				return
			}

			if resp == nil {
				t.Error("响应不能为空")
				return
			}

			// 验证响应字段
			if resp.RiskReportID == "" {
				t.Error("风控报告ID不能为空")
			}
			if resp.RiskScore < 0 || resp.RiskScore > 1000 {
				t.Errorf("风险分数超出范围: %d", resp.RiskScore)
			}

			if resp.RiskResult < 0 || resp.RiskResult > 3 {
				t.Errorf("风险结果超出范围: %d", resp.RiskResult)
			}
			if resp.EvaluationTime == "" {
				t.Error("评估时间不能为空")
			}

			// 特殊验证：检查产品匹配逻辑

		})
	}

	// 额外测试：风险分数比较逻辑
	t.Run("风险分数比较逻辑测试", func(t *testing.T) {
		t.Log("测试有借款记录用户的风险分数比较逻辑")

		// 先为用户创建一个评估记录
		customerID := uint64(11)
		params1 := EvaluateRiskWithProductParams{
			CustomerID: customerID,
			ProductID:  2,
			ChannelID:  uint64(2),
		}

		resp1, err := riskService.EvaluateRiskWithProduct(ctx, params1)
		if err != nil {
			t.Errorf("第一次评估失败: %v", err)
			return
		}

		t.Logf("第一次评估 - 风险分数: %d", resp1.RiskScore)

		// 等待一段时间后再次评估
		time.Sleep(100 * time.Millisecond)

		// resp2, err := riskService.EvaluateRiskWithProduct(ctx, params1)
		// if err != nil {
		// 	t.Errorf("第二次评估失败: %v", err)
		// 	return
		// }

		// t.Logf("第二次评估 - 风险分数: %.0f", resp2.RiskScore)

		// // 验证风险分数逻辑（有借款记录时应该取最小值）
		// if resp2.RiskScore > resp1.RiskScore {
		// 	t.Logf("风险分数比较正常: 第二次(%.0f) <= 第一次(%.0f)", resp2.RiskScore, resp1.RiskScore)
		// } else {
		// 	t.Logf("风险分数保持或降低: 第二次(%.0f), 第一次(%.0f)", resp2.RiskScore, resp1.RiskScore)
		// }
	})

	// 边界条件测试
	t.Run("边界条件测试", func(t *testing.T) {
		t.Log("测试各种边界条件")

		// 测试额度不足的情况（需要模拟一个高额度产品）
		if len(productResp.Products) > 1 {
			// 找到最高额度的产品
			maxProduct := productResp.Products[0]
			for _, p := range productResp.Products {
				if p.LoanAmount > maxProduct.LoanAmount {
					maxProduct = p
				}
			}

			params := EvaluateRiskWithProductParams{
				CustomerID: uint64(102 + time.Now().Unix()%1000), // 使用动态ID避免冲突
				ProductID:  uint64(maxProduct.ID),
				ChannelID:  uint64(0),
			}

			_, err := riskService.EvaluateRiskWithProduct(ctx, params)
			if err != nil {
				t.Logf("高额度产品测试出现错误（可能是额度不足）: %v", err)
			}
		}
	})
}
