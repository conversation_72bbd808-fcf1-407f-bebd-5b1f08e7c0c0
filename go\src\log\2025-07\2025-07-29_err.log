

[31m2025/07/29 16:06:23 [Recovery] 2025/07/29 - 16:06:23 panic recovered:
GET /uniapp/order/get_order_bills HTTP/1.1
Host: ************:8108
Accept: */*
Accept-Encoding: gzip, deflate, br
Connection: keep-alive
User-Agent: Apifox/1.0.0 (https://apifox.com)


token contains an invalid number of segments
D:/work/code/fincore/go/src/route/middleware/JwtVerify.go:111 (0x14781b6)
	ParseToken: panic(err)
D:/work/code/fincore/go/src/app/uniapp/order/controller.go:70 (0x1ad300c)
	(*Index).Get_order_bills: claim := middleware.ParseToken(c.GetHeader("Authorization"))
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:584 (0xac5ca4)
	Value.call: call(frametype, fn, stackArgs, uint32(frametype.Size()), uint32(abid.retOffset), uint32(frameSize), &regArgs)
C:/Users/<USER>/scoop/apps/go/current/src/reflect/value.go:368 (0xac4c52)
	Value.Call: return v.call("Call", in)
D:/work/code/fincore/go/src/utils/gf/AutoRouter.go:114 (0x13e21a4)
	match.func1: route.Method.Call(arguments)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x1139dd9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/route/middleware/LimitHandler.go:24 (0x1478904)
	LimitHandler.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x1139dd9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:176 (0x1276369)
	ErrorLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x1139dd9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x114d31c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x1139dd9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x114bbe5)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x1139dd9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/model/middleware.go:17 (0x14556b9)
	SQLContextMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x1139dd9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:142 (0x1276785)
	AccessLogMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x1139dd9)
	(*Context).Next: c.handlers[c.index](c)
D:/work/code/fincore/go/src/utils/log/middleware.go:40 (0x12778f5)
	RequestIDMiddleware.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x1139dd9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/recovery.go:101 (0x114d31c)
	CustomRecoveryWithWriter.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x1139dd9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/logger.go:240 (0x114bbe5)
	LoggerWithConfig.func1: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/context.go:173 (0x1139dd9)
	(*Context).Next: c.handlers[c.index](c)
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:616 (0x1149cc9)
	(*Engine).handleHTTPRequest: c.Next()
C:/Users/<USER>/go/pkg/mod/github.com/gin-gonic/gin@v1.8.1/gin.go:572 (0x114979b)
	(*Engine).ServeHTTP: engine.handleHTTPRequest(c)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:3301 (0xe624b6)
	serverHandler.ServeHTTP: handler.ServeHTTP(rw, req)
C:/Users/<USER>/scoop/apps/go/current/src/net/http/server.go:2102 (0xe2f374)
	(*conn).serve: serverHandler{c.server}.ServeHTTP(w, w.req)
C:/Users/<USER>/scoop/apps/go/current/src/runtime/asm_amd64.s:1700 (0x9b20e0)
	goexit: BYTE	$0x90	// NOP
[0m
