package payment

import (
	"fincore/utils/gform"
	"fincore/utils/jsonschema"
	"fincore/utils/results"
	"fmt"

	"github.com/gin-gonic/gin"
)

// 回调基础结构体
type CallbackBase struct {
	RespCode string `json:"resp_code"` // 响应码 成功返回 000000
	RespMsg  string `json:"resp_msg"`  // 响应码不为 0 返回失败原因
	SignType string `json:"sign_type"` // 签名类型 支持CERT和RSA必须大写,API接入方式使用CERT
	Sign     string `json:"sign"`      // 签名
}

// 代付回调通知
type DisbursementCallbackRequest struct {
	CallbackBase
	OrderNo       string `json:"order_no"`                        // 订单号
	TraceNo       string `json:"trace_no"`                        // 交易流水号
	OrderAmount   string `json:"order_amount"`                    // 订单金额 保留两位小数
	Status        string `json:"status"`                          // 状态 00 成功，01 处理中，03 失败
	FeeAmount     string `optional:"true" json:"fee"`             // 手续费 保留两位小数
	OboType       string `optional:"true" json:"obo_type"`        // 业务类型 01 代付，02 代收
	Remark        string `optional:"true" json:"remark"`          // 商户请求传入的remark, 原样返回
	TtfReturnCode string `optional:"true" json:"ttf_return_code"` // 失败时可能会返回
	TtfReturnMsg  string `optional:"true" json:"ttf_return_msg"`  // 失败时可能会返回
}

type DisbursementCallbackResponse struct {
	RespCode string `json:"resp_code"` // 响应码 成功返回 000000
	RespMsg  string `json:"resp_msg"`  // 响应码不为 0 返回失败原因
}

// DisbursementCallback 代付回调通知
func (c *Manager) DisbursementCallback(ctx *gin.Context) {
	req := DisbursementCallbackRequest{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.TtfFailedCallback(ctx, err, req)
		return
	}

	schema := GetDisbursementCallbackSchema()
	validator := jsonschema.NewValidator(schema)

	validationResult := validator.Validate(gform.StructToMap(req))
	if !validationResult.Valid {
		results.TtfFailedCallback(ctx, fmt.Errorf("参数验证失败"), req)
		return
	}

	// 调用 service 层处理业务逻辑
	paymentService := NewPaymentServiceWithOptions(
		WithContext(ctx),
		WithRepository(),
	)
	response, err := paymentService.HandleDisbursementCallback(&req)
	if err != nil {
		// 返回业务处理失败的响应，但HTTP状态码仍为200（符合回调接口规范）
		results.TtfFailedCallback(ctx, err, req)
		return
	}

	// 处理成功，返回成功响应
	results.TtfSuccessCallback(ctx, response)
}

// PaymentCallback 支付回调接口
func (c *Manager) PaymentCallback(ctx *gin.Context) {
	// 1. 参数绑定和验证
	var req PaymentCallbackRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.Failed(ctx, "参数绑定失败", err.Error())
		return
	}

	schema := GetPaymentCallbackSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(gform.StructToMap(req))
	if !validationResult.Valid {
		results.Failed(ctx, "支付回调通知", validationResult.Errors)
		return
	}

	// 调用 service 层处理业务逻辑
	paymentService := NewPaymentServiceWithOptions(
		WithContext(ctx),
		WithOrderModel(),
		WithTransactionModel(),
	)
	err := paymentService.HandlePaymentCallback(&req)
	if err != nil {
		results.TtfFailedCallback(ctx, err, req)
		return
	}

	results.TtfSuccessCallback(ctx, req)
}

// RefundCallback 退款通知回调
func (c *Manager) RefundCallback(ctx *gin.Context) {
	req := RefundCallbackRequest{}
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.TtfFailedCallback(ctx, err, req)
		return
	}

	schema := GetRefundCallbackSchema()
	validator := jsonschema.NewValidator(schema)

	validationResult := validator.Validate(gform.StructToMap(req))
	if !validationResult.Valid {
		results.TtfFailedCallback(ctx, fmt.Errorf("参数验证失败"), req)
		return
	}

	// 调用 service 层处理业务逻辑
	paymentService := NewPaymentServiceWithOptions(
		WithContext(ctx),
		WithRepository(),
	)
	response, err := paymentService.HandleRefundCallback(&req)
	if err != nil {
		// 返回业务处理失败的响应，但HTTP状态码仍为200（符合回调接口规范）
		results.TtfFailedCallback(ctx, err, req)
		return
	}

	// 处理成功，返回成功响应
	results.TtfSuccessCallback(ctx, response)
}
