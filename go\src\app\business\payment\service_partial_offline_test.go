package payment

import (
	"math"
	"testing"
)

// TestPartialOfflinePaymentAmountCalculation 测试部分线下支付金额拆分计算
func TestPartialOfflinePaymentAmountCalculation(t *testing.T) {
	tests := []struct {
		name                string
		totalDueAmount      float64
		dueGuaranteeFee     float64
		assetManagementFee  float64
		paymentAmount       float64
		expectedGuarantee   float64
		expectedAsset       float64
	}{
		{
			name:               "正常拆分测试",
			totalDueAmount:     1000.00,
			dueGuaranteeFee:    300.00,
			assetManagementFee: 700.00,
			paymentAmount:      500.00,
			expectedGuarantee:  150.00, // 500 * (300/1000) = 150
			expectedAsset:      350.00, // 500 - 150 = 350
		},
		{
			name:               "小数点精度测试",
			totalDueAmount:     333.33,
			dueGuaranteeFee:    111.11,
			assetManagementFee: 222.22,
			paymentAmount:      100.00,
			expectedGuarantee:  33.33, // 100 * (111.11/333.33) ≈ 33.33
			expectedAsset:      66.67, // 100 - 33.33 = 66.67
		},
		{
			name:               "担保费为0测试",
			totalDueAmount:     1000.00,
			dueGuaranteeFee:    0.00,
			assetManagementFee: 1000.00,
			paymentAmount:      500.00,
			expectedGuarantee:  0.00, // 500 * (0/1000) = 0
			expectedAsset:      500.00, // 500 - 0 = 500
		},
		{
			name:               "资管费为0测试",
			totalDueAmount:     1000.00,
			dueGuaranteeFee:    1000.00,
			assetManagementFee: 0.00,
			paymentAmount:      500.00,
			expectedGuarantee:  500.00, // 500 * (1000/1000) = 500
			expectedAsset:      0.00,   // 500 - 500 = 0
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 模拟计算逻辑
			guaranteeFeeRatio := tt.dueGuaranteeFee / tt.totalDueAmount
			guaranteeAmount := math.Round(tt.paymentAmount*guaranteeFeeRatio*100) / 100
			assetAmount := tt.paymentAmount - guaranteeAmount

			// 验证担保费金额
			if guaranteeAmount != tt.expectedGuarantee {
				t.Errorf("担保费金额计算错误: 期望 %.2f, 实际 %.2f", tt.expectedGuarantee, guaranteeAmount)
			}

			// 验证资管费金额
			if assetAmount != tt.expectedAsset {
				t.Errorf("资管费金额计算错误: 期望 %.2f, 实际 %.2f", tt.expectedAsset, assetAmount)
			}

			// 验证总金额一致性
			if guaranteeAmount+assetAmount != tt.paymentAmount {
				t.Errorf("拆分后总金额不一致: 担保费%.2f + 资管费%.2f = %.2f, 期望%.2f", 
					guaranteeAmount, assetAmount, guaranteeAmount+assetAmount, tt.paymentAmount)
			}
		})
	}
}
