package order

import (
	"fincore/model"
	"fincore/utils/convert"
	"fincore/utils/decimal"
	"fincore/utils/gform"
	"fincore/utils/repayment"
	"fmt"
	"time"
)

// OrderBillInfo 订单账单信息结构体
type OrderBillInfo struct {
	OrderID           int        `json:"order_id"`
	LoanDate          string     `json:"loan_date"`
	LoanAmount        float64    `json:"loan_amount"`
	TotalPeriods      int        `json:"total_periods"`
	DaysToNextDue     int        `json:"days_to_next_due"`
	RepaymentSchedule []BillItem `json:"repayment_schedule"`
}

// BillItem 账单项结构体
type BillItem struct {
	PeriodNumber    int     `json:"period_number"`
	DuePrincipal    float64 `json:"due_principal"`
	DueDate         string  `json:"due_date"`
	Status          int     `json:"status"`           // 原始状态值
	RepaymentStatus string  `json:"repayment_status"` // 转换后的状态文案
}

// OrderHistoryData 订单历史数据结构体（Repository层使用）
type OrderHistoryData struct {
	OrderID              int     `json:"order_id"`
	OrderNo              string  `json:"order_no"`
	Status               int     `json:"status"`
	TotalRepayableAmount float64 `json:"total_repayable_amount"`
	CreatedAt            string  `json:"created_at"`
}

// GetOrderBillsByOrderID 根据订单ID获取订单账单信息
func GetOrderBillsByOrderID(orderID int, userID int) (*OrderBillInfo, error) {
	// 检查数据库连接
	db := model.DB()
	if db == nil {
		return nil, fmt.Errorf("数据库连接失败")
	}

	// 1. 验证订单是否属于当前用户
	orderData, err := db.Table("business_loan_orders").
		Where("id", orderID).
		Where("user_id", userID).
		Fields("id, loan_amount, created_at").
		First()

	if err != nil {
		return nil, fmt.Errorf("查询订单信息失败: %v", err)
	}

	if orderData == nil {
		return nil, fmt.Errorf("订单不存在或无权限访问")
	}

	// 2. 获取还款计划列表 - 使用现有的Service
	billsService := model.NewBusinessRepaymentBillsService()
	bills, err := billsService.GetBillsByOrderID(nil, orderID)
	if err != nil {
		return nil, fmt.Errorf("查询还款计划失败: %v", err)
	}

	// 3. 构建返回数据
	result := &OrderBillInfo{
		OrderID:           orderID,
		LoanDate:          convert.ConvertToTime(orderData["created_at"]).Format("2006-01-02"),
		LoanAmount:        decimal.FormatAmount(convert.GetFloatFromMap(orderData, "loan_amount", 0)),
		TotalPeriods:      len(bills),
		DaysToNextDue:     0, // 将在Service层计算
		RepaymentSchedule: make([]BillItem, 0, len(bills)),
	}

	// 4. 构建还款计划列表
	for _, bill := range bills {
		item := BillItem{
			PeriodNumber:    bill.PeriodNumber,
			DuePrincipal:    decimal.FormatAmount(float64(bill.DuePrincipal)),
			DueDate:         bill.DueDate.Format("2006-01-02"),
			Status:          bill.Status,
			RepaymentStatus: repayment.GetRepaymentStatusText(bill.Status),
		}
		result.RepaymentSchedule = append(result.RepaymentSchedule, item)
	}

	return result, nil
}

// GetNextUnpaidBill 获取最近一期未还账单
func GetNextUnpaidBill(orderID int) (gform.Data, error) {
	// 检查数据库连接
	db := model.DB()
	if db == nil {
		return nil, fmt.Errorf("数据库连接失败")
	}

	return db.Table("business_repayment_bills").
		Where("order_id", orderID).
		Where("status", "in", []interface{}{0, 3, 4, 7}). // 待支付、逾期待支付、已取消、部分还款
		Fields("due_date").
		Order("period_number ASC").
		First()
}

// GetUserPendingOrders 根据用户ID获取待放款和放款中的订单
func GetUserPendingOrders(userID int) ([]OrderHistoryData, error) {
	// 检查数据库连接
	db := model.DB()
	if db == nil {
		return nil, fmt.Errorf("数据库连接失败")
	}

	// 参数验证
	if userID <= 0 {
		return nil, fmt.Errorf("用户ID无效")
	}

	// 查询用户待放款(0)和放款中(1)的订单，按创建时间倒序排列
	orderData, err := db.Table("business_loan_orders").
		Where("user_id", userID).
		Where("status", "in", []interface{}{0, 1}). // 只查询待放款和放款中的订单
		Fields("id, order_no, status, total_repayable_amount, created_at").
		OrderBy("created_at DESC").
		Get()

	if err != nil {
		return nil, fmt.Errorf("查询用户订单失败: %v", err)
	}

	// 转换数据格式
	var orders []OrderHistoryData
	for _, order := range orderData {
		// 获取订单ID
		orderID := convert.GetIntFromMap(order, "id", 0)

		// 获取订单编号
		orderNo := ""
		if orderNoVal, ok := order["order_no"]; ok {
			if orderNoStr, ok := orderNoVal.(string); ok {
				orderNo = orderNoStr
			}
		}

		// 获取订单状态
		status := convert.GetIntFromMap(order, "status", 0)

		// 获取还款总金额
		totalAmount := convert.GetFloatFromMap(order, "total_repayable_amount", 0.0)

		// 获取创建时间
		var createdAt string
		if createdTime, ok := order["created_at"]; ok {
			createdAt = createdTime.(time.Time).Format("2006-01-02 15:04:05")
		}

		orderHistory := OrderHistoryData{
			OrderID:              orderID,
			OrderNo:              orderNo,
			Status:               status,
			TotalRepayableAmount: totalAmount,
			CreatedAt:            createdAt,
		}

		orders = append(orders, orderHistory)
	}

	return orders, nil
}

// GetAllInLentOrderBillsByUserID 获取用户在途订单的待还款账单
func GetAllInLentOrderBillsByUserID(userID int) ([]AllOrderBillItem, error) {
	// 检查数据库连接
	db := model.DB()
	if db == nil {
		return nil, fmt.Errorf("数据库连接失败")
	}

	// 参数验证
	if userID <= 0 {
		return nil, fmt.Errorf("用户ID无效")
	}

	// 待还款账单状态：0-待支付，3-逾期待支付，7-部分还款
	data, err := db.Table("business_repayment_bills brb").
		LeftJoin("business_loan_orders blo", "brb.order_id = blo.id").
		Where("brb.user_id", userID).
		Where("blo.status", "in", []interface{}{0, 1}).    // 在途订单状态 0-待放款 1-放款中
		Where("brb.status", "in", []interface{}{0, 3, 7}). // 待还款账单状态
		Fields(`
			brb.id as bill_id,
			blo.created_at as loan_date,
			blo.loan_amount as order_loan_amount,
			blo.status as order_status,
			(brb.total_due_amount - brb.paid_amount - brb.total_waive_amount) as pending_amount,
			brb.due_date,
			(SELECT COUNT(*) FROM business_repayment_bills WHERE order_id = brb.order_id) as total_periods,
			brb.period_number as current_period
		`).
		Order("brb.due_date ASC, brb.period_number ASC").
		Get()

	if err != nil {
		return nil, fmt.Errorf("查询待还款账单失败: %v", err)
	}

	// 构建返回数据
	var result []AllOrderBillItem
	for _, item := range data {
		billItem := AllOrderBillItem{
			BillID:          convert.GetIntFromMap(item, "bill_id", 0),
			LoanDate:        convert.ConvertToTime(item["loan_date"]).Format("2006-01-02"),
			OrderLoanAmount: decimal.FormatAmount(convert.GetFloatFromMap(item, "order_loan_amount", 0)),
			OrderStatus:     convert.GetIntFromMap(item, "order_status", 0),
			PendingAmount:   decimal.FormatAmount(convert.GetFloatFromMap(item, "pending_amount", 0)),
			DueDate:         convert.ConvertToTime(item["due_date"]).Format("2006-01-02"),
			TotalPeriods:    convert.GetIntFromMap(item, "total_periods", 0),
			CurrentPeriod:   convert.GetIntFromMap(item, "current_period", 0),
		}
		result = append(result, billItem)
	}

	return result, nil
}
