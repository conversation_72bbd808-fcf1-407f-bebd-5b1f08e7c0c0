package external

import (
	"context"
	"fmt"
	"os"
	"path/filepath"
	"testing"

	"fincore/global"
	"fincore/model"

	"go.uber.org/zap"
)

// setupTestEnvironment 设置测试环境，切换到正确的工作目录
func setupTestEnvironment() (string, error) {
	path, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// 切换到src目录，确保能找到resource/config.yml
	srcPath := filepath.Join(path, "../../../")
	err = os.Chdir(srcPath)
	if err != nil {
		return "", err
	}

	return path, nil // 返回原始路径，用于defer恢复
}

// restoreWorkingDirectory 恢复原始工作目录
func restoreWorkingDirectory(originalPath string) {
	if originalPath != "" {
		os.Chdir(originalPath)
	}
}

func init() {
	// 设置测试环境
	_, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")
}

// TestThirdRiskIntegration 测试第三方风控系统集成
func TestThirdRiskIntegration(t *testing.T) {
	// 创建第三方风控服务实例
	service := NewGoDemoRiskService()

	// 构建测试数据
	testData := map[string]interface{}{
		"customer_id": "12345",
		"user_name":   "张三",
		"id_card":     "110101199001011234",
		"telephone":   "13800138000",
	}

	ctx := context.Background()

	// 测试风控评估
	result, err := service.EvaluateRisk(ctx, testData)
	if err != nil {
		t.Errorf("第三方风控评估失败: %v", err)
		return
	}

	// 验证结果
	if result == nil {
		t.Error("风控评估结果为空")
		return
	}

	// 测试结果映射
	riskResult, riskScore, failureReason := service.MapToRiskResult(result)

	fmt.Printf("风控评估结果:\n")
	fmt.Printf("  审核结果: %s\n", result.AuditResult)
	fmt.Printf("  拒绝原因: %s\n", result.DenyReason)
	fmt.Printf("  映射结果: %d\n", riskResult)
	fmt.Printf("  风险分数: %d\n", riskScore)
	fmt.Printf("  失败原因: %s\n", failureReason)
	fmt.Printf("  创建时间: %s\n", result.CreatedAt.Format("2006-01-02 15:04:05"))

	// 基本验证
	if riskResult < 0 || riskResult > 2 {
		t.Errorf("风险结果超出预期范围: %d", riskResult)
	}

	if riskScore < 0 || riskScore > 1000 {
		t.Errorf("风险分数超出预期范围: %d", riskScore)
	}

	t.Log("第三方风控系统集成测试通过")
}

// TestConfigLoading 测试配置加载
func TestConfigLoading(t *testing.T) {
	config, err := GetThirdRiskConfig()
	if err != nil {
		t.Fatalf("配置加载失败: %v", err)
	}
	if config == nil {
		t.Fatal("配置为空")
	}

	// 验证配置项
	if config.URL == "" {
		t.Error("URL配置为空")
	}
	if config.AppID == "" {
		t.Error("AppID配置为空")
	}
	if config.AESKey == "" {
		t.Error("AESKey配置为空")
	}

	t.Logf("配置加载成功: URL=%s, AppID=%s", config.URL, config.AppID)

	fmt.Printf("第三方风控配置:\n")
	fmt.Printf("  启用状态: %t\n", config.Enabled)
	fmt.Printf("  接口地址: %s\n", config.URL)
	fmt.Printf("  应用ID: %s\n", config.AppID)
	fmt.Printf("  超时时间: %d秒\n", config.Timeout)

	t.Log("配置加载测试通过")
}

// BenchmarkThirdRiskEvaluation 性能测试
func BenchmarkThirdRiskEvaluation(b *testing.B) {
	service := NewGoDemoRiskService()
	testData := map[string]interface{}{
		"customer_id": "12345",
		"user_name":   "张三",
		"id_card":     "110101199001011234",
		"telephone":   "13800138000",
	}

	ctx := context.Background()

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := service.EvaluateRisk(ctx, testData)
		if err != nil {
			b.Errorf("第三方风控评估失败: %v", err)
		}
	}
}
