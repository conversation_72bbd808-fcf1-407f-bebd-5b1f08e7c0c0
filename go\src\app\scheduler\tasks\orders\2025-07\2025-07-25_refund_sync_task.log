[2025-07-25 15:29:25.850]	.info	退款状态同步补偿任务即将开始	{"task": "refund-status-sync-compensation", "operation": "task_starting"}
[2025-07-25 15:29:25.850]	.info	退款状态同步补偿任务执行成功	{"task": "refund-status-sync-compensation", "operation": "task_success"}
[2025-07-25 15:29:25.850]	.error	退款状态同步补偿任务执行失败	{"task": "refund-status-sync-compensation", "operation": "task_error", "error": "context canceled"}
fincore/utils/log.(*Logger).Error
	D:/work/code/fincore/go/src/utils/log/context.go:171
command-line-arguments.(*RefundStatusSyncCompensationTask).OnError
	D:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation.go:231
command-line-arguments.TestRefundStatusSyncCompensationTask_Callbacks
	D:/work/code/fincore/go/src/app/scheduler/tasks/orders/refund_status_sync_compensation_test.go:93
testing.tRunner
	C:/Users/<USER>/scoop/apps/go/current/src/testing/testing.go:1792
