package blacklist

import (
	"context"
	"os"
	"path/filepath"
	"testing"

	"fincore/global"
	"fincore/model"

	"go.uber.org/zap"
)

// setupTestEnvironment 设置测试环境，切换到正确的工作目录
func setupTestEnvironment() (string, error) {
	path, err := os.Getwd()
	if err != nil {
		return "", err
	}

	// 切换到src目录，确保能找到resource/config.yml
	srcPath := filepath.Join(path, "../../../")
	err = os.Chdir(srcPath)
	if err != nil {
		return "", err
	}

	return path, nil // 返回原始路径，用于defer恢复
}

// restoreWorkingDirectory 恢复原始工作目录
func restoreWorkingDirectory(originalPath string) {
	if originalPath != "" {
		os.Chdir(originalPath)
	}
}

// TestBlacklistSystemIntegration 测试黑名单系统的主要功能
func TestBlacklistSystemIntegration(t *testing.T) {
	t.Log("=== 金融信贷黑名单系统测试 ===")

	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)

	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 跳过数据库初始化，使用模拟数据
	model.MyInit("test") // 注释掉数据库初始化，避免配置依赖

	// 创建黑名单服务
	blacklistService := NewBlacklistService()

	ctx := context.Background()
	testCustomerID := uint64(17)

	// 测试黑名单检查
	t.Run("黑名单检查测试", func(t *testing.T) {
		result, err := blacklistService.CheckUserBlacklist(ctx, testCustomerID)
		if err != nil {
			t.Errorf("黑名单检查失败: %v", err)
			return
		}

		t.Logf("黑名单检查成功:")
		t.Logf("  是否在黑名单中: %v", result.IsBlacklisted)
		t.Logf("  黑名单类型: %s", result.BlacklistType)
		t.Logf("  风险原因数量: %d", len(result.Reasons))
		for i, reason := range result.Reasons {
			t.Logf("  原因%d: %s", i+1, reason)
		}
		t.Logf("  详细信息数量: %d", len(result.Details))
		for key, value := range result.Details {
			t.Logf("  %s: %s", key, value)
		}

		// 验证返回结果的基本有效性
		if result == nil {
			t.Error("黑名单检查结果不能为空")
		}
		if result.Reasons == nil {
			t.Error("风险原因列表不能为空")
		}
		if result.Details == nil {
			t.Error("详细信息不能为空")
		}
	})

	// 测试黑名单统计信息
	t.Run("黑名单统计信息测试", func(t *testing.T) {
		summary, err := blacklistService.GetBlacklistSummary(ctx)
		if err != nil {
			t.Errorf("获取黑名单统计信息失败: %v", err)
			return
		}

		t.Logf("黑名单统计信息:")
		if totalChecked, ok := summary["total_checked"]; ok {
			t.Logf("  总检查数量: %v", totalChecked)
		}
		if aTypeCount, ok := summary["a_type_count"]; ok {
			t.Logf("  A类黑名单数量: %v", aTypeCount)
		}
		if bTypeCount, ok := summary["b_type_count"]; ok {
			t.Logf("  B类黑名单数量: %v", bTypeCount)
		}
		if reasonsStats, ok := summary["reasons_stats"]; ok {
			t.Logf("  原因统计: %v", reasonsStats)
		}

		// 验证统计信息结构
		if summary == nil {
			t.Error("统计信息不能为空")
		}
	})

	t.Log("=== 测试完成 ===")
}

// TestBlacklistCheckOnly 单独测试黑名单检查功能
func TestBlacklistCheckOnly(t *testing.T) {
	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)

	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")

	// 初始化服务
	blacklistService := NewBlacklistService()

	ctx := context.Background()
	testCases := []struct {
		name       string
		customerID uint64
		expectErr  bool
	}{
		{"正常客户", 17, false},
		{"高风险客户", 18, false},
		{"空客户ID", 0, false}, // 空客户ID不应该报错，只是没有数据
		{"不存在客户", 9999, false},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			result, err := blacklistService.CheckUserBlacklist(ctx, tc.customerID)
			if tc.expectErr {
				if err == nil {
					t.Error("期望出现错误，但没有错误")
				}
				t.Logf("预期错误: %v", err)
				return
			}

			if err != nil {
				t.Errorf("不期望出现错误: %v", err)
				return
			}

			if result == nil {
				t.Error("响应不能为空")
				return
			}

			// 验证响应字段
			if result.Reasons == nil {
				t.Error("风险原因列表不能为空")
			}
			if result.Details == nil {
				t.Error("详细信息不能为空")
			}

			t.Logf("检查结果: 客户ID=%d, 是否黑名单=%v, 类型=%s, 原因数量=%d",
				tc.customerID, result.IsBlacklisted, result.BlacklistType, len(result.Reasons))
		})
	}
}

// TestOverdueStatusCheck 测试逾期状态检查功能
func TestOverdueStatusCheck(t *testing.T) {
	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)

	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")

	// 初始化服务
	blacklistService := NewBlacklistService()

	ctx := context.Background()

	testCases := []struct {
		name        string
		customerID  uint64
		expectErr   bool
		description string
	}{
		{"正常客户逾期检查", 17, false, "检查存在的客户逾期状态"},
		{"不存在客户", 9999, false, "检查不存在的客户"},
		{"空客户ID", 0, false, "测试空客户ID处理"},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			t.Logf("测试用例: %s - %s", tc.name, tc.description)

			result := &BlacklistResult{
				IsBlacklisted: false,
				Reasons:       []BlacklistReason{},
				Details:       make(map[string]string),
			}

			err := blacklistService.checkOverdueStatus(ctx, tc.customerID, result)
			if tc.expectErr {
				if err == nil {
					t.Error("期望出现错误，但没有错误")
				}
				t.Logf("预期错误: %v", err)
				return
			}

			if err != nil {
				t.Errorf("不期望出现错误: %v", err)
				return
			}

			t.Logf("逾期检查结果: 客户ID=%d, 是否黑名单=%v, 类型=%s, 原因数量=%d",
				tc.customerID, result.IsBlacklisted, result.BlacklistType, len(result.Reasons))

			for key, value := range result.Details {
				t.Logf("  详细信息: %s = %s", key, value)
			}
		})
	}
}

// TestRiskFactorsCheck 测试各种风险因素检查
func TestRiskFactorsCheck(t *testing.T) {
	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)

	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")

	// 初始化服务
	blacklistService := NewBlacklistService()

	ctx := context.Background()
	testCustomerID := uint64(17)

	t.Run("身份证风险检查", func(t *testing.T) {
		result := &BlacklistResult{
			IsBlacklisted: false,
			Reasons:       []BlacklistReason{},
			Details:       make(map[string]string),
		}

		err := blacklistService.checkIDCardRisk(ctx, testCustomerID, result)
		if err != nil {
			t.Logf("身份证风险检查出现错误: %v", err)
		}

		t.Logf("身份证风险检查结果: 是否黑名单=%v, 类型=%s", result.IsBlacklisted, result.BlacklistType)
		for key, value := range result.Details {
			t.Logf("  详细信息: %s = %s", key, value)
		}
	})

	t.Run("地址风险检查", func(t *testing.T) {
		result := &BlacklistResult{
			IsBlacklisted: false,
			Reasons:       []BlacklistReason{},
			Details:       make(map[string]string),
		}

		err := blacklistService.checkAddressRisk(ctx, testCustomerID, result)
		if err != nil {
			t.Logf("地址风险检查出现错误: %v", err)
		}

		t.Logf("地址风险检查结果: 是否黑名单=%v, 类型=%s", result.IsBlacklisted, result.BlacklistType)
		for key, value := range result.Details {
			t.Logf("  详细信息: %s = %s", key, value)
		}
	})

	t.Run("卡Bin风险检查", func(t *testing.T) {
		result := &BlacklistResult{
			IsBlacklisted: false,
			Reasons:       []BlacklistReason{},
			Details:       make(map[string]string),
		}

		err := blacklistService.checkCardBinRisk(ctx, testCustomerID, result)
		if err != nil {
			t.Logf("卡Bin风险检查出现错误: %v", err)
		}

		t.Logf("卡Bin风险检查结果: 是否黑名单=%v, 类型=%s", result.IsBlacklisted, result.BlacklistType)
		for key, value := range result.Details {
			t.Logf("  详细信息: %s = %s", key, value)
		}
	})

	t.Run("手机号码风险检查", func(t *testing.T) {
		result := &BlacklistResult{
			IsBlacklisted: false,
			Reasons:       []BlacklistReason{},
			Details:       make(map[string]string),
		}

		err := blacklistService.checkMobileRisk(ctx, testCustomerID, result)
		if err != nil {
			t.Logf("手机号码风险检查出现错误: %v", err)
		}

		t.Logf("手机号码风险检查结果: 是否黑名单=%v, 类型=%s", result.IsBlacklisted, result.BlacklistType)
		for key, value := range result.Details {
			t.Logf("  详细信息: %s = %s", key, value)
		}
	})
}

// TestRiskValidationFunctions 测试风险验证函数
func TestRiskValidationFunctions(t *testing.T) {
	blacklistService := NewBlacklistService()
	t.Run("身份证风险验证", func(t *testing.T) {
		testCases := []struct {
			idCard   string
			expected bool
			desc     string
		}{
			{"123456199012345678", false, "正常身份证"},
			{"12345678901234567", true, "长度不足"},
			{"1234567890123456789", true, "长度过长"},
			{"123456200012345678", false, "正常年龄"},
			{"123456195012345678", true, "年龄过大"},
			{"123456201012345678", true, "年龄过小"},
		}

		for _, tc := range testCases {
			result := blacklistService.isIDCardRisky(tc.idCard)
			if result != tc.expected {
				t.Errorf("%s: 期望 %v, 得到 %v", tc.desc, tc.expected, result)
			}
			t.Logf("%s: %s -> %v", tc.desc, tc.idCard, result)
		}
	})

	t.Run("手机号码风险验证", func(t *testing.T) {
		testCases := []struct {
			mobile   string
			expected bool
			desc     string
		}{
			{"13812345678", false, "正常手机号"},
			{"15912345678", false, "正常手机号"},
			{"12312345678", true, "非法前缀"},
			{"12", true, "长度不足"},
			{"", true, "空号码"},
		}

		for _, tc := range testCases {
			result := blacklistService.isMobileRisky(tc.mobile)
			if result != tc.expected {
				t.Errorf("%s: 期望 %v, 得到 %v", tc.desc, tc.expected, result)
			}
			t.Logf("%s: %s -> %v", tc.desc, tc.mobile, result)
		}
	})

	t.Run("卡Bin风险验证", func(t *testing.T) {
		testCases := []struct {
			cardNo   string
			expected bool
			desc     string
		}{
			{"6224151234567890", true, "风险卡Bin"},
			{"6230981234567890", true, "风险卡Bin"},
			{"6217661234567890", true, "风险卡Bin"},
			{"6225001234567890", false, "正常卡Bin"},
			{"12345", true, "长度不足"},
			{"", true, "空卡号"},
		}

		for _, tc := range testCases {
			result := blacklistService.isCardBinRisky(tc.cardNo)
			if result != tc.expected {
				t.Errorf("%s: 期望 %v, 得到 %v", tc.desc, tc.expected, result)
			}
			t.Logf("%s: %s -> %v", tc.desc, tc.cardNo, result)
		}
	})
}

// TestDateParsing 测试日期解析功能
func TestDateParsing(t *testing.T) {
	blacklistService := NewBlacklistService()

	testCases := []struct {
		dateStr   string
		expectErr bool
		desc      string
	}{
		{"2025-01-15", false, "标准日期格式"},
		{"2025-01-15 10:30:00", false, "日期时间格式"},
		{"2025/01/15", false, "斜杠分隔格式"},
		{"01/15/2025", false, "美式日期格式"},
		{"15-01-2025", false, "欧式日期格式"},
		{"2025-1-15", false, "无前导零格式"},
		{"invalid-date", true, "无效日期格式"},
		{"", true, "空日期"},
	}

	for _, tc := range testCases {
		t.Run(tc.desc, func(t *testing.T) {
			result, err := blacklistService.parseDueDate(tc.dateStr)
			if tc.expectErr {
				if err == nil {
					t.Errorf("%s: 期望出现错误，但没有错误", tc.desc)
				}
				t.Logf("%s: %s -> 错误: %v", tc.desc, tc.dateStr, err)
			} else {
				if err != nil {
					t.Errorf("%s: 不期望出现错误: %v", tc.desc, err)
				}
				t.Logf("%s: %s -> %v", tc.desc, tc.dateStr, result)
			}
		})
	}
}

// BenchmarkBlacklistCheck 性能测试
func BenchmarkBlacklistCheck(b *testing.B) {
	// 设置测试环境
	originalPath, err := setupTestEnvironment()
	if err != nil {
		panic(err)
	}
	defer restoreWorkingDirectory(originalPath)

	// 初始化日志系统
	logger, _ := zap.NewDevelopment()
	global.App.Log = logger

	// 初始化数据库连接
	model.MyInit("test")

	// 初始化服务
	blacklistService := NewBlacklistService()
	ctx := context.Background()
	testCustomerID := uint64(17)

	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		_, err := blacklistService.CheckUserBlacklist(ctx, testCustomerID)
		if err != nil {
			b.Errorf("黑名单检查失败: %v", err)
		}
	}
}