package orders

import (
	"context"
	"testing"
	"time"

	"fincore/utils/log"
)

func TestRefundStatusSyncCompensationTask_Execute(t *testing.T) {
	// 创建任务实例
	task := NewRefundStatusSyncCompensationTask()

	// 验证任务基本配置
	if task.GetName() != "refund-status-sync-compensation" {
		t.<PERSON>("Expected task name 'refund-status-sync-compensation', got '%s'", task.GetName())
	}

	if task.GetDescription() != "退款状态同步补偿任务 - 处理已提交退款流水的状态同步" {
		t.<PERSON><PERSON>rf("Task description mismatch")
	}

	// 验证调度表达式
	if task.GetSchedule() != "0 */1 * * * *" {
		t.<PERSON><PERSON>("Expected schedule '0 */1 * * * *', got '%s'", task.GetSchedule())
	}

	// 验证超时时间
	if task.GetTimeout() != 5*time.Minute {
		t.<PERSON>("Expected timeout 5 minutes, got %v", task.GetTimeout())
	}
}

func TestRefundStatusSyncCompensationTask_Structure(t *testing.T) {
	// 创建任务实例
	task := NewRefundStatusSyncCompensationTask()

	// 验证任务结构
	if task.BaseTask == nil {
		t.Error("BaseTask should not be nil")
	}

	if task.paymentService == nil {
		t.Error("paymentService should not be nil")
	}

	if task.transactionModel == nil {
		t.Error("transactionModel should not be nil")
	}

	if task.logger == nil {
		t.Error("logger should not be nil")
	}

	t.Logf("Task structure validation passed for: %s", task.GetName())
}

func TestRefundStatusSyncCompensationTask_Methods(t *testing.T) {
	// 创建任务实例
	task := NewRefundStatusSyncCompensationTask()

	// 验证任务可以正常创建和配置
	if task == nil {
		t.Error("Task should not be nil")
		return
	}

	// 验证任务配置
	if task.GetRetryCount() != 3 {
		t.Errorf("Expected retry count 3, got %d", task.GetRetryCount())
	}

	t.Log("Task methods and configuration validated")
}

func TestRefundStatusSyncCompensationTask_Callbacks(t *testing.T) {
	// 创建任务实例
	task := NewRefundStatusSyncCompensationTask()
	ctx := context.Background()

	// 测试回调方法
	err := task.OnStart(ctx)
	if err != nil {
		t.Errorf("OnStart failed: %v", err)
	}

	err = task.OnSuccess(ctx)
	if err != nil {
		t.Errorf("OnSuccess failed: %v", err)
	}

	err = task.OnError(ctx, context.Canceled)
	if err != nil {
		t.Errorf("OnError failed: %v", err)
	}
}

// 基准测试：测试任务创建性能
func BenchmarkNewRefundStatusSyncCompensationTask(b *testing.B) {
	b.ResetTimer()
	for i := 0; i < b.N; i++ {
		task := NewRefundStatusSyncCompensationTask()
		_ = task
	}
}

// 示例：展示如何使用任务
func ExampleRefundStatusSyncCompensationTask() {
	// 创建任务
	task := NewRefundStatusSyncCompensationTask()

	// 创建上下文
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Minute)
	defer cancel()

	// 执行任务（在实际环境中，这会由调度器自动执行）
	err := task.Execute(ctx)
	if err != nil {
		log.Error("Task execution failed: %v", err)
		return
	}

	log.Info("Task executed successfully")
}
