package order

import (
	"testing"

	"fincore/utils/pagination"
)

// TestGetOrderListWithFilters 测试订单列表查询（包含账单到期时间筛选）
func TestGetOrderListWithFilters(t *testing.T) {
	repository := NewRepository()

	// 测试基本查询
	params := map[string]interface{}{
		"page":     1,
		"pageSize": 10,
	}

	paginationReq := pagination.PaginationRequest{
		Page:     1,
		PageSize: 10,
	}
	paginationReq.ValidateAndNormalize()

	result, err := repository.GetOrderListWithFilters(params, paginationReq)
	if err != nil {
		t.<PERSON><PERSON><PERSON>("GetOrderListWithFilters failed: %v", err)
		return
	}

	if result == nil {
		t.Error("Result should not be nil")
		return
	}

	t.Logf("基本查询成功，总数: %d", result.Total)
}

// TestGetOrderListWithBillDueDateFilter 测试账单到期时间筛选
func TestGetOrderListWithBillDueDateFilter(t *testing.T) {
	repository := NewRepository()

	// 测试账单到期时间筛选
	params := map[string]interface{}{
		"page":                1,
		"pageSize":            10,
		"bill_due_date_start": "2025-01-01",
		"bill_due_date_end":   "2025-12-31",
	}

	paginationReq := pagination.PaginationRequest{
		Page:     1,
		PageSize: 10,
	}
	paginationReq.ValidateAndNormalize()

	result, err := repository.GetOrderListWithFilters(params, paginationReq)
	if err != nil {
		t.Errorf("GetOrderListWithFilters with bill due date filter failed: %v", err)
		return
	}

	if result == nil {
		t.Error("Result should not be nil")
		return
	}

	t.Logf("账单到期时间筛选查询成功，总数: %d", result.Total)
}

// TestApplyOrderListFilters 测试筛选条件应用
func TestApplyOrderListFilters(t *testing.T) {
	repository := NewRepository()

	// 测试多种筛选条件
	params := map[string]interface{}{
		"order_no":              "TEST",
		"status":                2,
		"bill_due_date_start":   "2025-01-01",
		"bill_due_date_end":     "2025-12-31",
		"loan_amount_min":       1000,
		"loan_amount_max":       50000,
		"submitted_at_start":    "2025-01-01 00:00:00",
		"submitted_at_end":      "2025-12-31 23:59:59",
	}

	paginationReq := pagination.PaginationRequest{
		Page:     1,
		PageSize: 5,
	}
	paginationReq.ValidateAndNormalize()

	result, err := repository.GetOrderListWithFilters(params, paginationReq)
	if err != nil {
		t.Errorf("GetOrderListWithFilters with multiple filters failed: %v", err)
		return
	}

	if result == nil {
		t.Error("Result should not be nil")
		return
	}

	t.Logf("多条件筛选查询成功，总数: %d", result.Total)
}

// TestGetRiskScoresBatch 测试批量获取风控分数
func TestGetRiskScoresBatch(t *testing.T) {
	repository := NewRepository()

	// 测试空用户ID列表
	emptyResult, err := repository.GetRiskScoresBatch([]int{})
	if err != nil {
		t.Errorf("GetRiskScoresBatch with empty userIDs failed: %v", err)
		return
	}

	if len(emptyResult) != 0 {
		t.Error("Empty userIDs should return empty result")
		return
	}

	t.Log("空用户ID列表测试通过")

	// 测试有效用户ID列表
	userIDs := []int{1, 2, 3, 999999} // 包含一些可能不存在的ID
	result, err := repository.GetRiskScoresBatch(userIDs)
	if err != nil {
		t.Errorf("GetRiskScoresBatch failed: %v", err)
		return
	}

	if result == nil {
		t.Error("Result should not be nil")
		return
	}

	t.Logf("批量获取风控分数成功，获取到 %d 个用户的风控分数", len(result))

	// 打印结果用于调试
	for userID, riskScore := range result {
		t.Logf("用户ID: %d, 风控分数: %d", userID, riskScore)
	}
}
