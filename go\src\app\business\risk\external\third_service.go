package external

import (
	"context"
	"encoding/json"
	"fmt"
	"net/url"
	"strconv"
	"time"

	"fincore/global"
	"fincore/model"

	"go.uber.org/zap"
)

// GoDemoRiskService 第三方风控服务
type GoDemoRiskService struct {
	config    *ThirdRiskConfig
	aesUtils  *AESUtils
	httpUtils *HTTPUtils
}

// NewGoDemoRiskService 创建第三方风控服务实例
func NewGoDemoRiskService() *GoDemoRiskService {
	config, err := GetThirdRiskConfig()
	if err != nil {
		global.App.Log.Error("获取第三方风控配置失败", zap.Error(err))
		// 使用默认配置
		config = &ThirdRiskConfig{
			Enabled:     false,
			URL:         "http://riskopenapi.eenz.cn/",
			AppID:       "KH202507241277780636309716992",
			AESKey:      "vIGit3Mxy1+ZA3/RiA8YpQ==",
			Timeout:     30,
			ServiceCode: "risk_queryV2",
			DataSource:  "third_party",
		}
	}
	return &GoDemoRiskService{
		config:    config,
		aesUtils:  NewAESUtils(),
		httpUtils: NewHTTPUtils(config.Timeout),
	}
}

// EvaluateRisk 执行风控评估
func (s *GoDemoRiskService) EvaluateRisk(ctx context.Context, data map[string]interface{}) (*GoDemoResult, error) {
	// 检查服务是否启用
	if !s.config.Enabled {
		return nil, fmt.Errorf("第三方风控服务未启用")
	}

	// 从data中提取用户信息
	customerID, ok := data["customer_id"].(string)
	if !ok {
		return nil, fmt.Errorf("customer_id参数无效")
	}

	// 获取客户信息
	customerInfo, err := s.getCustomerInfo(customerID)
	if err != nil {
		return nil, fmt.Errorf("获取客户信息失败: %v", err)
	}

	// 构建风控查询请求
	req := ThirdRiskQueryRequest{
		Service:   s.config.ServiceCode,
		UserName:  customerInfo.Name,
		IDCard:    customerInfo.IDCard,
		Telephone: customerInfo.Mobile,
	}

	// 调用第三方风控接口
	response, err := s.QueryRisk(req)
	if err != nil {
		return nil, fmt.Errorf("调用第三方风控接口失败: %v", err)
	}

	// 解析响应并构建结果
	result, err := s.parseResponse(response)
	if err != nil {
		return nil, fmt.Errorf("解析响应失败: %v", err)
	}

	return result, nil
}

// QueryRisk 执行风控查询
func (s *GoDemoRiskService) QueryRisk(req ThirdRiskQueryRequest) (*ThirdRiskResponse, error) {
	// 1. 准备业务数据（明文）
	plainText, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("failed to marshal request: %v", err)
	}

	global.App.Log.Debug("第三方风控业务数据明文", zap.String("plaintext", string(plainText)))

	// 2. 加密（使用接收方分配给该appId的AES密钥）
	encryptedData, err := s.aesUtils.Encrypt(string(plainText), s.config.AESKey)
	if err != nil {
		return nil, fmt.Errorf("failed to encrypt data: %v", err)
	}

	global.App.Log.Debug("第三方风控加密后数据", zap.String("encrypted", encryptedData))

	// 3. 对加密后的数据进行URL编码
	encodedBizData := url.QueryEscape(encryptedData)
	global.App.Log.Debug("第三方风控URL编码后数据", zap.String("encoded", encodedBizData))

	// 4. 构建请求体（符合ApiCommonRequest结构）
	requestBody := ThirdAPICommonRequest{
		AppID:   s.config.AppID,
		BizData: encodedBizData,
	}

	// 5. 发送请求
	response, err := s.httpUtils.SendPostJSON(s.config.URL, requestBody)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %v", err)
	}

	global.App.Log.Debug("第三方风控响应结果", zap.String("response", response))

	// 6. 解析响应
	var riskResp ThirdRiskResponse
	if err := json.Unmarshal([]byte(response), &riskResp); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}

	return &riskResp, nil
}

// parseResponse 解析第三方风控响应
func (s *GoDemoRiskService) parseResponse(response *ThirdRiskResponse) (*GoDemoResult, error) {
	result := &GoDemoResult{
		CreatedAt: time.Now(),
	}

	// 检查响应是否成功
	if !response.IsSuccess() {
		return nil, fmt.Errorf("第三方风控接口返回错误: %s - %s", response.Code, response.GetErrorMessage())
	}

	// 解析业务数据
	if response.Data != nil {
		result.AuditResult = response.Data.AuditResult
		result.DenyReason = response.Data.DenyReason

		// 解析各个数据源的数据
		if response.Data.LeidaV4 != nil {
			leidaData, _ := json.Marshal(response.Data.LeidaV4)
			result.LeidaV4Data = string(leidaData)
		}

		if response.Data.TanZhenC != nil {
			tanZhenData, _ := json.Marshal(response.Data.TanZhenC)
			result.TanZhenCData = string(tanZhenData)
		}

		if response.Data.Zwsc != nil {
			zwscData, _ := json.Marshal(response.Data.Zwsc)
			result.ZwscData = string(zwscData)
		}
	}

	return result, nil
}

// getCustomerInfo 获取客户信息
func (s *GoDemoRiskService) getCustomerInfo(customerID string) (*model.BusinessAppAccount, error) {
	// 这里应该调用客户服务获取客户信息
	// 暂时使用模拟数据，实际实现时需要调用相应的服务
	customerService := model.NewBusinessAppAccountService()

	// 将字符串ID转换为int64
	id, err := strconv.ParseInt(customerID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("无效的客户ID: %v", err)
	}

	customerInfo, err := customerService.GetBusinessAppAccountByID(id)
	if err != nil {
		return nil, err
	}

	if customerInfo == nil {
		return nil, fmt.Errorf("客户不存在")
	}

	return customerInfo, nil
}

// MapToRiskResult 将第三方风控结果映射到系统风险评估结果
func (s *GoDemoRiskService) MapToRiskResult(result *GoDemoResult) (int, int, string) {
	var riskResult int
	var riskScore int
	failureReason := result.DenyReason

	// 根据审核结果映射风险评估结果
	switch result.AuditResult {
	case "OK":
		riskResult = model.APPROVED
		riskScore = 1000
	case "MANUAL":
		riskResult = model.REVIEW
		riskScore = 500
	case "DENY":
		riskResult = model.REJECTED
		riskScore = 0
	default:
		// 未知结果，标记为调用失败
		riskResult = model.CALLRISKMODELFAILED
		riskScore = 0
		failureReason = fmt.Sprintf("未知的审核结果: %s", result.AuditResult)
	}

	return riskResult, riskScore, failureReason
}
