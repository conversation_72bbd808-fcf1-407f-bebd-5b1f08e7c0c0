package repayment

import (
	"reflect"

	"fincore/app/business/repayment"
	"fincore/route/middleware"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
)

// CreateRepaymentRequest 创建还款支付请求
type CreateRepaymentRequest struct {
	BillID     int `json:"bill_id" binding:"required"`      // 账单ID
	BankCardID int `json:"bank_card_id" binding:"required"` // 银行卡ID
}

// floatPtr 返回float64指针，用于jsonschema的Min/Max字段
func floatPtr(f float64) *float64 {
	return &f
}

// GetCreateRepaymentSchema 获取创建还款支付的验证规则
func GetCreateRepaymentSchema() *jsonschema.Schema {
	return &jsonschema.Schema{
		Title: "创建还款支付参数",
		Type:  "object",
		Properties: map[string]jsonschema.ValidateRule{
			"bill_id": {
				Type:        "int",
				Required:    true,
				Min:         floatPtr(1),
				Description: "账单ID（必填，正整数）",
			},
			"bank_card_id": {
				Type:        "int",
				Required:    true,
				Min:         floatPtr(1),
				Description: "银行卡ID（必填，正整数）",
			},
		},
		Required: []string{"bill_id", "bank_card_id"},
	}
}

type PaymentController struct{}

func init() {
	controller := PaymentController{}
	gf.Register(&controller, reflect.TypeOf(controller).PkgPath())
}

// CreateRepayment 创建还款支付
func (pc *PaymentController) CreateRepayment(ctx *gin.Context) {
	// 1. 参数绑定和验证
	var req CreateRepaymentRequest
	if err := ctx.ShouldBindJSON(&req); err != nil {
		results.Failed(ctx, "参数绑定失败", err.Error())
		return
	}

	// 2. 参数验证
	schema := GetCreateRepaymentSchema()
	validator := jsonschema.NewValidator(*schema)

	// 将结构体转换为map进行验证
	reqMap := map[string]interface{}{
		"bill_id":      req.BillID,
		"bank_card_id": req.BankCardID,
	}

	validationResult := validator.Validate(reqMap)
	if !validationResult.Valid {
		results.Failed(ctx, "参数验证失败", validationResult.Errors)
		return
	}

	// 3. 调用服务层处理业务逻辑
	service := repayment.NewPaymentService()
	result, err := service.CreateRepayment(ctx, req.BillID, req.BankCardID)
	if err != nil {
		results.Failed(ctx, "创建还款支付失败", err.Error())
		return
	}

	results.Success(ctx, "创建还款支付成功", result, nil)
}

// GetPaymentStatus 查询支付状态
func (pc *PaymentController) GetPaymentStatus(ctx *gin.Context) {
	// 1. 验证用户权限
	claim := middleware.ParseToken(ctx.GetHeader("Authorization"))
	if claim == nil {
		results.Failed(ctx, "用户未登录", "")
		return
	}

	// 2. 获取交易流水号
	transactionNo := ctx.Query("transaction_no")
	if transactionNo == "" {
		results.Failed(ctx, "参数错误", "交易流水号不能为空")
		return
	}

	// 3. 调用业务层查询支付状态
	businessService := repayment.NewPaymentService()
	response, err := businessService.QueryPaymentStatus(transactionNo)
	if err != nil {
		results.Failed(ctx, "查询支付状态失败", err.Error())
		return
	}

	// 4. 返回成功响应
	results.Success(ctx, "查询支付状态成功", response, nil)
}
