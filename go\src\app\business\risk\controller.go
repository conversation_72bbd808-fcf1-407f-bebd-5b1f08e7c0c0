package risk

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"reflect"
	"strconv"
	"strings"
	"sync"
	"time"

	"fincore/app/business/channel"
	"fincore/app/business/risk/external"
	"fincore/global"
	"fincore/model"
	"fincore/thirdparty/riskmodelservice"
	"fincore/thirdparty/riskthirdparty"
	"fincore/utils/gf"
	"fincore/utils/jsonschema"
	"fincore/utils/results"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

// RiskController 风控控制器
type RiskController struct{}

// 初始化风控服务

var (
	riskService *RiskService
	once        sync.Once
)

func init() {
	// 注册路由到自动路由系统
	gf.Register(&RiskController{}, reflect.TypeOf(RiskController{}).PkgPath())
}

// 获取风控服务实例（延迟初始化）
func GetRiskService() *RiskService {
	once.Do(func() {
		if !isTestEnvironment() {
			// 初始化服务依赖
			riskEvalService := model.NewRiskEvaluationService()
			productService := model.NewProductRulesService()
			channelService := channel.NewChannelService()
			thirdPartyService := riskthirdparty.NewRiskThirdPartyService()
			riskModelService := riskmodelservice.NewRiskModelService()

			// 创建风控服务
			riskService = NewRiskService(
				riskEvalService,
				productService,
				channelService,
				thirdPartyService,
				riskModelService,
			)
		}
	})
	return riskService
}

// isTestEnvironment 检测是否为测试环境
func isTestEnvironment() bool {
	for _, arg := range os.Args {
		if strings.Contains(arg, "test") {
			return true
		}
	}
	return false
}

// GetEvaluate 授信评估接口
// 路由: GET /business/risk/riskcontroller/getEvaluate
func (rc *RiskController) GetEvaluate(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := GetRiskEvaluationSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	thirdConfig, err := external.GetThirdRiskConfig()
	if err != nil {
		global.App.Log.Error("获取第三方风控配置失败", zap.Error(err))
		results.Failed(c, "配置错误", err.Error())
		return
	}
	useThird := thirdConfig.Enabled

	var result interface{}

	if useThird {
		// 使用第三方风控服务
		result, err = rc.evaluateWithThirdService(ctx, validationResult.Data)
	} else {
		// 使用原有风控服务
		// 优先从缓存获取风控评估结果
		result, err = GetRiskService().GetCachedRiskEvaluation(ctx, validationResult.Data)
		if err != nil {
			// 缓存未命中或失败，进行新的风控评估
			result, err = GetRiskService().EvaluateRisk(ctx, validationResult.Data)
		}
	}

	if err != nil {
		results.Failed(c, "风控评估失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "评估成功", result, nil)
}

// GetProducts 贷款产品匹配接口
// 路由: GET /business/risk/get_products
func (rc *RiskController) GetProducts(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
		"channel_id":  c.Query("channel_id"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := GetLoanProductsSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := GetRiskService().GetLoanProducts(ctx, validationResult.Data)
	if err != nil {
		results.Failed(c, "获取产品列表失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "获取成功", result, nil)
}

// GetReports 风控报告查询接口
// 路由: GET /business/risk/get_reports
func (rc *RiskController) GetReports(c *gin.Context) {
	// 1. 参数校验
	requestData := map[string]interface{}{
		"customer_id": c.Query("customer_id"),
		"start_date":  c.Query("start_date"),
		"end_date":    c.Query("end_date"),
	}

	// 移除空值
	cleanData := make(map[string]interface{})
	for k, v := range requestData {
		if str, ok := v.(string); ok && str != "" {
			cleanData[k] = str
		}
	}

	schema := GetRiskReportSchema()
	validator := jsonschema.NewValidator(schema)
	validationResult := validator.Validate(cleanData)
	if !validationResult.Valid {
		results.Failed(c, "参数验证失败", validationResult.Errors)
		return
	}

	// 2. 业务逻辑处理
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	result, err := GetRiskService().GetRiskReports(ctx, validationResult.Data)
	if err != nil {
		// 判断是否为记录不存在的错误
		if err.Error() == "未找到风控评估记录" || err.Error() == "客户不存在" {
			results.Failed(c, err.Error(), nil)
			return
		}
		results.Failed(c, "获取风控报告失败", err.Error())
		return
	}

	// 3. 返回结果
	results.Success(c, "获取成功", result, map[string]interface{}{"customer_id": requestData["customer_id"]})
}

// evaluateWithThirdService 使用第三方风控服务进行评估
func (rc *RiskController) evaluateWithThirdService(ctx context.Context, data map[string]interface{}) (*RiskEvaluationResponse, error) {
	// 获取客户ID
	customerIDStr, ok := data["customer_id"].(string)
	if !ok {
		return nil, fmt.Errorf("customer_id参数无效")
	}

	customerID, err := strconv.Atoi(customerIDStr)
	if err != nil {
		return nil, fmt.Errorf("customer_id格式错误: %v", err)
	}

	// 创建第三方风控服务实例
	thirdService := external.NewGoDemoRiskService()

	// 调用第三方风控服务
	thirdResult, err := thirdService.EvaluateRisk(ctx, data)
	if err != nil {
		global.App.Log.Error("第三方风控服务调用失败",
			zap.Int("customer_id", customerID),
			zap.Error(err))
		return nil, fmt.Errorf("第三方风控服务调用失败: %v", err)
	}

	// 生成评估ID
	evaluationID := model.GenerateEvaluationID(int(customerID))

	// 映射风控结果
	riskResult, riskScore, failureReason := thirdService.MapToRiskResult(thirdResult)

	// 存储原始数据到risk_raw_data表
	if err := rc.storeThirdPartyRawData(evaluationID, thirdResult); err != nil {
		global.App.Log.Error("存储第三方风控原始数据失败",
			zap.String("evaluation_id", evaluationID),
			zap.Error(err))
		// 不阻断流程，继续执行
	}

	// 构建评估结果
	evaluation := &model.RiskEvaluation{
		EvaluationID:   evaluationID,
		CustomerID:     int(customerID),
		RiskScore:      riskScore,
		RiskResult:     riskResult,
		EvaluationTime: time.Now(),
	}

	// 如果有失败原因，设置失败信息
	if failureReason != "" {
		failureType := "THIRD_PARTY_REJECT"
		evaluation.FailureType = &failureType
		evaluation.FailureReason = &failureReason
	}

	// 存储评估结果到risk_evaluations表
	riskEvalService := model.NewRiskEvaluationService()
	if err := riskEvalService.CreateEvaluation(evaluation); err != nil {
		global.App.Log.Error("存储第三方风控评估结果失败",
			zap.String("evaluation_id", evaluationID),
			zap.Error(err))
		return nil, fmt.Errorf("存储评估结果失败: %v", err)
	}

	// 构建响应
	response := &RiskEvaluationResponse{
		RiskReportID:   evaluationID,
		RiskScore:      riskScore,
		RiskResult:     riskResult,
		EvaluationTime: evaluation.EvaluationTime.Format("2006-01-02 15:04:05"),
	}

	if evaluation.FailureType != nil {
		response.FailureType = evaluation.FailureType
	}
	if evaluation.FailureReason != nil {
		response.FailureReason = evaluation.FailureReason
	}

	global.App.Log.Info("第三方风控评估完成",
		zap.Int("customer_id", customerID),
		zap.String("evaluation_id", evaluationID),
		zap.Int("risk_result", riskResult),
		zap.Int("risk_score", riskScore))

	return response, nil
}

// storeThirdPartyRawData 存储第三方风控原始数据
func (rc *RiskController) storeThirdPartyRawData(evaluationID string, result *external.GoDemoResult) error {
	// 构建原始数据
	rawData := &model.RiskRawData{
		EvaluationID:     evaluationID,
		LeidaV4Response:  result.LeidaV4Data,
		TanZhenCResponse: result.TanZhenCData,
		ZwscResponse:     result.ZwscData,
		DataSource:       "third_party",
		CreatedAt:        result.CreatedAt,
		UpdatedAt:        result.CreatedAt,
	}

	// 存储到数据库
	riskEvalService := model.NewRiskEvaluationService()
	return riskEvalService.CreateRawData(rawData)
}

// GetHealth 健康检查接口
// 路由: GET /business/risk/get_health
func (rc *RiskController) GetHealth(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"status":  "ok",
		"service": "fincore-risk-service",
		"version": "1.0.0",
		"time":    time.Now().Format("2006-01-02 15:04:05"),
	})
}
