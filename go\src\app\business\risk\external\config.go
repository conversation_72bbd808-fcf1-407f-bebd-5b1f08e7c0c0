package external

import (
	"fincore/global"
)

// ThirdRiskConfig 第三方风控配置
type ThirdRiskConfig struct {
	Enabled     bool   `yaml:"enabled"`      // 是否启用第三方风控服务
	URL         string `yaml:"url"`          // 第三方风控接口地址
	AppID       string `yaml:"app_id"`       // 应用ID（对应MerchantID）
	AESKey      string `yaml:"aes_key"`      // AES加密密钥
	Timeout     int    `yaml:"timeout"`      // 请求超时时间（秒）
	ServiceCode string `yaml:"service_code"` // 服务码
	DataSource  string `yaml:"data_source"`  // 数据源标识
}

// GetThirdRiskConfig 获取第三方风控配置
func GetThirdRiskConfig() (*ThirdRiskConfig, error) {
	// 从全局配置中获取第三方风控配置
	riskThirdParty := global.App.Config.RiskThirdParty
	
	config := &ThirdRiskConfig{
		Enabled:     true, // 默认启用，可以根据需要调整
		URL:         riskThirdParty.URL,
		AppID:       riskThirdParty.MerchantID, // MerchantID对应AppID
		AESKey:      riskThirdParty.AESKey,
		Timeout:     riskThirdParty.Timeout,
		ServiceCode: "risk_queryV2", // 默认服务码
		DataSource:  "third_party",  // 默认数据源
	}

	return config, nil
}
